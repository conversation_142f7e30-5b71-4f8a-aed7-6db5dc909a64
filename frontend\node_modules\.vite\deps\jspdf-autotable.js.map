{"version": 3, "sources": ["../../jspdf-autotable/dist/jspdf.plugin.autotable.mjs"], "sourcesContent": ["/**\n * Improved text function with halign and valign support\n * Inspiration from: http://stackoverflow.com/questions/28327510/align-text-right-using-jspdf/28433113#28433113\n */\nfunction autoTableText (text, x, y, styles, doc) {\n    styles = styles || {};\n    var PHYSICAL_LINE_HEIGHT = 1.15;\n    var k = doc.internal.scaleFactor;\n    var fontSize = doc.internal.getFontSize() / k;\n    var lineHeightFactor = doc.getLineHeightFactor\n        ? doc.getLineHeightFactor()\n        : PHYSICAL_LINE_HEIGHT;\n    var lineHeight = fontSize * lineHeightFactor;\n    var splitRegex = /\\r\\n|\\r|\\n/g;\n    var splitText = '';\n    var lineCount = 1;\n    if (styles.valign === 'middle' ||\n        styles.valign === 'bottom' ||\n        styles.halign === 'center' ||\n        styles.halign === 'right') {\n        splitText = typeof text === 'string' ? text.split(splitRegex) : text;\n        lineCount = splitText.length || 1;\n    }\n    // Align the top\n    y += fontSize * (2 - PHYSICAL_LINE_HEIGHT);\n    if (styles.valign === 'middle')\n        y -= (lineCount / 2) * lineHeight;\n    else if (styles.valign === 'bottom')\n        y -= lineCount * lineHeight;\n    if (styles.halign === 'center' || styles.halign === 'right') {\n        var alignSize = fontSize;\n        if (styles.halign === 'center')\n            alignSize *= 0.5;\n        if (splitText && lineCount >= 1) {\n            for (var iLine = 0; iLine < splitText.length; iLine++) {\n                doc.text(splitText[iLine], x - doc.getStringUnitWidth(splitText[iLine]) * alignSize, y);\n                y += lineHeight;\n            }\n            return doc;\n        }\n        x -= doc.getStringUnitWidth(text) * alignSize;\n    }\n    if (styles.halign === 'justify') {\n        doc.text(text, x, y, { maxWidth: styles.maxWidth || 100, align: 'justify' });\n    }\n    else {\n        doc.text(text, x, y);\n    }\n    return doc;\n}\n\nvar globalDefaults = {};\nvar DocHandler = /** @class */ (function () {\n    function DocHandler(jsPDFDocument) {\n        this.jsPDFDocument = jsPDFDocument;\n        this.userStyles = {\n            // Black for versions of jspdf without getTextColor\n            textColor: jsPDFDocument.getTextColor\n                ? this.jsPDFDocument.getTextColor()\n                : 0,\n            fontSize: jsPDFDocument.internal.getFontSize(),\n            fontStyle: jsPDFDocument.internal.getFont().fontStyle,\n            font: jsPDFDocument.internal.getFont().fontName,\n            // 0 for versions of jspdf without getLineWidth\n            lineWidth: jsPDFDocument.getLineWidth\n                ? this.jsPDFDocument.getLineWidth()\n                : 0,\n            // Black for versions of jspdf without getDrawColor\n            lineColor: jsPDFDocument.getDrawColor\n                ? this.jsPDFDocument.getDrawColor()\n                : 0,\n        };\n    }\n    DocHandler.setDefaults = function (defaults, doc) {\n        if (doc === void 0) { doc = null; }\n        if (doc) {\n            doc.__autoTableDocumentDefaults = defaults;\n        }\n        else {\n            globalDefaults = defaults;\n        }\n    };\n    DocHandler.unifyColor = function (c) {\n        if (Array.isArray(c)) {\n            return c;\n        }\n        else if (typeof c === 'number') {\n            return [c, c, c];\n        }\n        else if (typeof c === 'string') {\n            return [c];\n        }\n        else {\n            return null;\n        }\n    };\n    DocHandler.prototype.applyStyles = function (styles, fontOnly) {\n        // Font style needs to be applied before font\n        // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/632\n        var _a, _b, _c;\n        if (fontOnly === void 0) { fontOnly = false; }\n        if (styles.fontStyle && this.jsPDFDocument.setFontStyle) {\n            this.jsPDFDocument.setFontStyle(styles.fontStyle);\n        }\n        var _d = this.jsPDFDocument.internal.getFont(), fontStyle = _d.fontStyle, fontName = _d.fontName;\n        if (styles.font)\n            fontName = styles.font;\n        if (styles.fontStyle) {\n            fontStyle = styles.fontStyle;\n            var availableFontStyles = this.getFontList()[fontName];\n            if (availableFontStyles &&\n                availableFontStyles.indexOf(fontStyle) === -1 &&\n                this.jsPDFDocument.setFontStyle) {\n                // Common issue was that the default bold in headers\n                // made custom fonts not work. For example:\n                // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/653\n                this.jsPDFDocument.setFontStyle(availableFontStyles[0]);\n                fontStyle = availableFontStyles[0];\n            }\n        }\n        this.jsPDFDocument.setFont(fontName, fontStyle);\n        if (styles.fontSize)\n            this.jsPDFDocument.setFontSize(styles.fontSize);\n        if (fontOnly) {\n            return; // Performance improvement\n        }\n        var color = DocHandler.unifyColor(styles.fillColor);\n        if (color)\n            (_a = this.jsPDFDocument).setFillColor.apply(_a, color);\n        color = DocHandler.unifyColor(styles.textColor);\n        if (color)\n            (_b = this.jsPDFDocument).setTextColor.apply(_b, color);\n        color = DocHandler.unifyColor(styles.lineColor);\n        if (color)\n            (_c = this.jsPDFDocument).setDrawColor.apply(_c, color);\n        if (typeof styles.lineWidth === 'number') {\n            this.jsPDFDocument.setLineWidth(styles.lineWidth);\n        }\n    };\n    DocHandler.prototype.splitTextToSize = function (text, size, opts) {\n        return this.jsPDFDocument.splitTextToSize(text, size, opts);\n    };\n    /**\n     * Adds a rectangle to the PDF\n     * @param x Coordinate (in units declared at inception of PDF document) against left edge of the page\n     * @param y Coordinate (in units declared at inception of PDF document) against upper edge of the page\n     * @param width Width (in units declared at inception of PDF document)\n     * @param height Height (in units declared at inception of PDF document)\n     * @param fillStyle A string specifying the painting style or null. Valid styles include: 'S' [default] - stroke, 'F' - fill, and 'DF' (or 'FD') - fill then stroke.\n     */\n    DocHandler.prototype.rect = function (x, y, width, height, fillStyle) {\n        // null is excluded from fillStyle possible values because it isn't needed\n        // and is prone to bugs as it's used to postpone setting the style\n        // https://rawgit.com/MrRio/jsPDF/master/docs/jsPDF.html#rect\n        return this.jsPDFDocument.rect(x, y, width, height, fillStyle);\n    };\n    DocHandler.prototype.getLastAutoTable = function () {\n        return this.jsPDFDocument.lastAutoTable || null;\n    };\n    DocHandler.prototype.getTextWidth = function (text) {\n        return this.jsPDFDocument.getTextWidth(text);\n    };\n    DocHandler.prototype.getDocument = function () {\n        return this.jsPDFDocument;\n    };\n    DocHandler.prototype.setPage = function (page) {\n        this.jsPDFDocument.setPage(page);\n    };\n    DocHandler.prototype.addPage = function () {\n        return this.jsPDFDocument.addPage();\n    };\n    DocHandler.prototype.getFontList = function () {\n        return this.jsPDFDocument.getFontList();\n    };\n    DocHandler.prototype.getGlobalOptions = function () {\n        return globalDefaults || {};\n    };\n    DocHandler.prototype.getDocumentOptions = function () {\n        return this.jsPDFDocument.__autoTableDocumentDefaults || {};\n    };\n    DocHandler.prototype.pageSize = function () {\n        var pageSize = this.jsPDFDocument.internal.pageSize;\n        // JSPDF 1.4 uses get functions instead of properties on pageSize\n        if (pageSize.width == null) {\n            pageSize = { width: pageSize.getWidth(), height: pageSize.getHeight() };\n        }\n        return pageSize;\n    };\n    DocHandler.prototype.scaleFactor = function () {\n        return this.jsPDFDocument.internal.scaleFactor;\n    };\n    DocHandler.prototype.getLineHeightFactor = function () {\n        var doc = this.jsPDFDocument;\n        return doc.getLineHeightFactor ? doc.getLineHeightFactor() : 1.15;\n    };\n    DocHandler.prototype.getLineHeight = function (fontSize) {\n        return (fontSize / this.scaleFactor()) * this.getLineHeightFactor();\n    };\n    DocHandler.prototype.pageNumber = function () {\n        var pageInfo = this.jsPDFDocument.internal.getCurrentPageInfo();\n        if (!pageInfo) {\n            // Only recent versions of jspdf has pageInfo\n            return this.jsPDFDocument.internal.getNumberOfPages();\n        }\n        return pageInfo.pageNumber;\n    };\n    return DocHandler;\n}());\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nvar HtmlRowInput = /** @class */ (function (_super) {\n    __extends(HtmlRowInput, _super);\n    function HtmlRowInput(element) {\n        var _this = _super.call(this) || this;\n        _this._element = element;\n        return _this;\n    }\n    return HtmlRowInput;\n}(Array));\n// Base style for all themes\nfunction defaultStyles(scaleFactor) {\n    return {\n        font: 'helvetica', // helvetica, times, courier\n        fontStyle: 'normal', // normal, bold, italic, bolditalic\n        overflow: 'linebreak', // linebreak, ellipsize, visible or hidden\n        fillColor: false, // Either false for transparent, rbg array e.g. [255, 255, 255] or gray level e.g 200\n        textColor: 20,\n        halign: 'left', // left, center, right, justify\n        valign: 'top', // top, middle, bottom\n        fontSize: 10,\n        cellPadding: 5 / scaleFactor, // number or {top,left,right,left,vertical,horizontal}\n        lineColor: 200,\n        lineWidth: 0,\n        cellWidth: 'auto', // 'auto'|'wrap'|number\n        minCellHeight: 0,\n        minCellWidth: 0,\n    };\n}\nfunction getTheme(name) {\n    var themes = {\n        striped: {\n            table: { fillColor: 255, textColor: 80, fontStyle: 'normal' },\n            head: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\n            body: {},\n            foot: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\n            alternateRow: { fillColor: 245 },\n        },\n        grid: {\n            table: {\n                fillColor: 255,\n                textColor: 80,\n                fontStyle: 'normal',\n                lineWidth: 0.1,\n            },\n            head: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0,\n            },\n            body: {},\n            foot: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0,\n            },\n            alternateRow: {},\n        },\n        plain: { head: { fontStyle: 'bold' }, foot: { fontStyle: 'bold' } },\n    };\n    return themes[name];\n}\n\nfunction getStringWidth(text, styles, doc) {\n    doc.applyStyles(styles, true);\n    var textArr = Array.isArray(text) ? text : [text];\n    var widestLineWidth = textArr\n        .map(function (text) { return doc.getTextWidth(text); })\n        .reduce(function (a, b) { return Math.max(a, b); }, 0);\n    return widestLineWidth;\n}\nfunction addTableBorder(doc, table, startPos, cursor) {\n    var lineWidth = table.settings.tableLineWidth;\n    var lineColor = table.settings.tableLineColor;\n    doc.applyStyles({ lineWidth: lineWidth, lineColor: lineColor });\n    var fillStyle = getFillStyle(lineWidth, false);\n    if (fillStyle) {\n        doc.rect(startPos.x, startPos.y, table.getWidth(doc.pageSize().width), cursor.y - startPos.y, fillStyle);\n    }\n}\nfunction getFillStyle(lineWidth, fillColor) {\n    var drawLine = lineWidth > 0;\n    var drawBackground = fillColor || fillColor === 0;\n    if (drawLine && drawBackground) {\n        return 'DF'; // Fill then stroke\n    }\n    else if (drawLine) {\n        return 'S'; // Only stroke (transparent background)\n    }\n    else if (drawBackground) {\n        return 'F'; // Only fill, no stroke\n    }\n    else {\n        return null;\n    }\n}\nfunction parseSpacing(value, defaultValue) {\n    var _a, _b, _c, _d;\n    value = value || defaultValue;\n    if (Array.isArray(value)) {\n        if (value.length >= 4) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[3],\n            };\n        }\n        else if (value.length === 3) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[1],\n            };\n        }\n        else if (value.length === 2) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[0],\n                left: value[1],\n            };\n        }\n        else if (value.length === 1) {\n            value = value[0];\n        }\n        else {\n            value = defaultValue;\n        }\n    }\n    if (typeof value === 'object') {\n        if (typeof value.vertical === 'number') {\n            value.top = value.vertical;\n            value.bottom = value.vertical;\n        }\n        if (typeof value.horizontal === 'number') {\n            value.right = value.horizontal;\n            value.left = value.horizontal;\n        }\n        return {\n            left: (_a = value.left) !== null && _a !== void 0 ? _a : defaultValue,\n            top: (_b = value.top) !== null && _b !== void 0 ? _b : defaultValue,\n            right: (_c = value.right) !== null && _c !== void 0 ? _c : defaultValue,\n            bottom: (_d = value.bottom) !== null && _d !== void 0 ? _d : defaultValue,\n        };\n    }\n    if (typeof value !== 'number') {\n        value = defaultValue;\n    }\n    return { top: value, right: value, bottom: value, left: value };\n}\nfunction getPageAvailableWidth(doc, table) {\n    var margins = parseSpacing(table.settings.margin, 0);\n    return doc.pageSize().width - (margins.left + margins.right);\n}\n\n// Limitations\n// - No support for border spacing\n// - No support for transparency\nfunction parseCss(supportedFonts, element, scaleFactor, style, window) {\n    var result = {};\n    var pxScaleFactor = 96 / 72;\n    var backgroundColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)['backgroundColor'];\n    });\n    if (backgroundColor != null)\n        result.fillColor = backgroundColor;\n    var textColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)['color'];\n    });\n    if (textColor != null)\n        result.textColor = textColor;\n    var padding = parsePadding(style, scaleFactor);\n    if (padding)\n        result.cellPadding = padding;\n    var borderColorSide = 'borderTopColor';\n    var finalScaleFactor = pxScaleFactor * scaleFactor;\n    var btw = style.borderTopWidth;\n    if (style.borderBottomWidth === btw &&\n        style.borderRightWidth === btw &&\n        style.borderLeftWidth === btw) {\n        var borderWidth = (parseFloat(btw) || 0) / finalScaleFactor;\n        if (borderWidth)\n            result.lineWidth = borderWidth;\n    }\n    else {\n        result.lineWidth = {\n            top: (parseFloat(style.borderTopWidth) || 0) / finalScaleFactor,\n            right: (parseFloat(style.borderRightWidth) || 0) / finalScaleFactor,\n            bottom: (parseFloat(style.borderBottomWidth) || 0) / finalScaleFactor,\n            left: (parseFloat(style.borderLeftWidth) || 0) / finalScaleFactor,\n        };\n        // Choose border color of first available side\n        // could be improved by supporting object as lineColor\n        if (!result.lineWidth.top) {\n            if (result.lineWidth.right) {\n                borderColorSide = 'borderRightColor';\n            }\n            else if (result.lineWidth.bottom) {\n                borderColorSide = 'borderBottomColor';\n            }\n            else if (result.lineWidth.left) {\n                borderColorSide = 'borderLeftColor';\n            }\n        }\n    }\n    var borderColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)[borderColorSide];\n    });\n    if (borderColor != null)\n        result.lineColor = borderColor;\n    var accepted = ['left', 'right', 'center', 'justify'];\n    if (accepted.indexOf(style.textAlign) !== -1) {\n        result.halign = style.textAlign;\n    }\n    accepted = ['middle', 'bottom', 'top'];\n    if (accepted.indexOf(style.verticalAlign) !== -1) {\n        result.valign = style.verticalAlign;\n    }\n    var res = parseInt(style.fontSize || '');\n    if (!isNaN(res))\n        result.fontSize = res / pxScaleFactor;\n    var fontStyle = parseFontStyle(style);\n    if (fontStyle)\n        result.fontStyle = fontStyle;\n    var font = (style.fontFamily || '').toLowerCase();\n    if (supportedFonts.indexOf(font) !== -1) {\n        result.font = font;\n    }\n    return result;\n}\nfunction parseFontStyle(style) {\n    var res = '';\n    if (style.fontWeight === 'bold' ||\n        style.fontWeight === 'bolder' ||\n        parseInt(style.fontWeight) >= 700) {\n        res = 'bold';\n    }\n    if (style.fontStyle === 'italic' || style.fontStyle === 'oblique') {\n        res += 'italic';\n    }\n    return res;\n}\nfunction parseColor(element, styleGetter) {\n    var cssColor = realColor(element, styleGetter);\n    if (!cssColor)\n        return null;\n    var rgba = cssColor.match(/^rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*(\\d*\\.?\\d*))?\\)$/);\n    if (!rgba || !Array.isArray(rgba)) {\n        return null;\n    }\n    var color = [\n        parseInt(rgba[1]),\n        parseInt(rgba[2]),\n        parseInt(rgba[3]),\n    ];\n    var alpha = parseInt(rgba[4]);\n    if (alpha === 0 || isNaN(color[0]) || isNaN(color[1]) || isNaN(color[2])) {\n        return null;\n    }\n    return color;\n}\nfunction realColor(elem, styleGetter) {\n    var bg = styleGetter(elem);\n    if (bg === 'rgba(0, 0, 0, 0)' ||\n        bg === 'transparent' ||\n        bg === 'initial' ||\n        bg === 'inherit') {\n        if (elem.parentElement == null) {\n            return null;\n        }\n        return realColor(elem.parentElement, styleGetter);\n    }\n    else {\n        return bg;\n    }\n}\nfunction parsePadding(style, scaleFactor) {\n    var val = [\n        style.paddingTop,\n        style.paddingRight,\n        style.paddingBottom,\n        style.paddingLeft,\n    ];\n    var pxScaleFactor = 96 / (72 / scaleFactor);\n    var linePadding = (parseInt(style.lineHeight) - parseInt(style.fontSize)) / scaleFactor / 2;\n    var inputPadding = val.map(function (n) {\n        return parseInt(n || '0') / pxScaleFactor;\n    });\n    var padding = parseSpacing(inputPadding, 0);\n    if (linePadding > padding.top) {\n        padding.top = linePadding;\n    }\n    if (linePadding > padding.bottom) {\n        padding.bottom = linePadding;\n    }\n    return padding;\n}\n\nfunction parseHtml(doc, input, window, includeHiddenHtml, useCss) {\n    var _a, _b;\n    if (includeHiddenHtml === void 0) { includeHiddenHtml = false; }\n    if (useCss === void 0) { useCss = false; }\n    var tableElement;\n    if (typeof input === 'string') {\n        tableElement = window.document.querySelector(input);\n    }\n    else {\n        tableElement = input;\n    }\n    var supportedFonts = Object.keys(doc.getFontList());\n    var scaleFactor = doc.scaleFactor();\n    var head = [], body = [], foot = [];\n    if (!tableElement) {\n        console.error('Html table could not be found with input: ', input);\n        return { head: head, body: body, foot: foot };\n    }\n    for (var i = 0; i < tableElement.rows.length; i++) {\n        var element = tableElement.rows[i];\n        var tagName = (_b = (_a = element === null || element === void 0 ? void 0 : element.parentElement) === null || _a === void 0 ? void 0 : _a.tagName) === null || _b === void 0 ? void 0 : _b.toLowerCase();\n        var row = parseRowContent(supportedFonts, scaleFactor, window, element, includeHiddenHtml, useCss);\n        if (!row)\n            continue;\n        if (tagName === 'thead') {\n            head.push(row);\n        }\n        else if (tagName === 'tfoot') {\n            foot.push(row);\n        }\n        else {\n            // Add to body both if parent is tbody or table\n            body.push(row);\n        }\n    }\n    return { head: head, body: body, foot: foot };\n}\nfunction parseRowContent(supportedFonts, scaleFactor, window, row, includeHidden, useCss) {\n    var resultRow = new HtmlRowInput(row);\n    for (var i = 0; i < row.cells.length; i++) {\n        var cell = row.cells[i];\n        var style_1 = window.getComputedStyle(cell);\n        if (includeHidden || style_1.display !== 'none') {\n            var cellStyles = void 0;\n            if (useCss) {\n                cellStyles = parseCss(supportedFonts, cell, scaleFactor, style_1, window);\n            }\n            resultRow.push({\n                rowSpan: cell.rowSpan,\n                colSpan: cell.colSpan,\n                styles: cellStyles,\n                _element: cell,\n                content: parseCellContent(cell),\n            });\n        }\n    }\n    var style = window.getComputedStyle(row);\n    if (resultRow.length > 0 && (includeHidden || style.display !== 'none')) {\n        return resultRow;\n    }\n}\nfunction parseCellContent(orgCell) {\n    // Work on cloned node to make sure no changes are applied to html table\n    var cell = orgCell.cloneNode(true);\n    // Remove extra space and line breaks in markup to make it more similar to\n    // what would be shown in html\n    cell.innerHTML = cell.innerHTML.replace(/\\n/g, '').replace(/ +/g, ' ');\n    // Preserve <br> tags as line breaks in the pdf\n    cell.innerHTML = cell.innerHTML\n        .split(/<br.*?>/) //start with '<br' and ends with '>'.\n        .map(function (part) { return part.trim(); })\n        .join('\\n');\n    // innerText for ie\n    return cell.innerText || cell.textContent || '';\n}\n\nfunction validateInput(global, document, current) {\n    for (var _i = 0, _a = [global, document, current]; _i < _a.length; _i++) {\n        var options = _a[_i];\n        if (options && typeof options !== 'object') {\n            console.error('The options parameter should be of type object, is: ' + typeof options);\n        }\n        if (options.startY && typeof options.startY !== 'number') {\n            console.error('Invalid value for startY option', options.startY);\n            delete options.startY;\n        }\n    }\n}\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\nfunction assign(target, s, s1, s2, s3) {\n    if (target == null) {\n        throw new TypeError('Cannot convert undefined or null to object');\n    }\n    var to = Object(target);\n    for (var index = 1; index < arguments.length; index++) {\n        // eslint-disable-next-line prefer-rest-params\n        var nextSource = arguments[index];\n        if (nextSource != null) {\n            // Skip over if undefined or null\n            for (var nextKey in nextSource) {\n                // Avoid bugs when hasOwnProperty is shadowed\n                if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\n                    to[nextKey] = nextSource[nextKey];\n                }\n            }\n        }\n    }\n    return to;\n}\n\nfunction parseInput(d, current) {\n    var doc = new DocHandler(d);\n    var document = doc.getDocumentOptions();\n    var global = doc.getGlobalOptions();\n    validateInput(global, document, current);\n    var options = assign({}, global, document, current);\n    var win;\n    if (typeof window !== 'undefined') {\n        win = window;\n    }\n    var styles = parseStyles(global, document, current);\n    var hooks = parseHooks(global, document, current);\n    var settings = parseSettings(doc, options);\n    var content = parseContent$1(doc, options, win);\n    return { id: current.tableId, content: content, hooks: hooks, styles: styles, settings: settings };\n}\nfunction parseStyles(gInput, dInput, cInput) {\n    var styleOptions = {\n        styles: {},\n        headStyles: {},\n        bodyStyles: {},\n        footStyles: {},\n        alternateRowStyles: {},\n        columnStyles: {},\n    };\n    var _loop_1 = function (prop) {\n        if (prop === 'columnStyles') {\n            var global_1 = gInput[prop];\n            var document_1 = dInput[prop];\n            var current = cInput[prop];\n            styleOptions.columnStyles = assign({}, global_1, document_1, current);\n        }\n        else {\n            var allOptions = [gInput, dInput, cInput];\n            var styles = allOptions.map(function (opts) { return opts[prop] || {}; });\n            styleOptions[prop] = assign({}, styles[0], styles[1], styles[2]);\n        }\n    };\n    for (var _i = 0, _a = Object.keys(styleOptions); _i < _a.length; _i++) {\n        var prop = _a[_i];\n        _loop_1(prop);\n    }\n    return styleOptions;\n}\nfunction parseHooks(global, document, current) {\n    var allOptions = [global, document, current];\n    var result = {\n        didParseCell: [],\n        willDrawCell: [],\n        didDrawCell: [],\n        willDrawPage: [],\n        didDrawPage: [],\n    };\n    for (var _i = 0, allOptions_1 = allOptions; _i < allOptions_1.length; _i++) {\n        var options = allOptions_1[_i];\n        if (options.didParseCell)\n            result.didParseCell.push(options.didParseCell);\n        if (options.willDrawCell)\n            result.willDrawCell.push(options.willDrawCell);\n        if (options.didDrawCell)\n            result.didDrawCell.push(options.didDrawCell);\n        if (options.willDrawPage)\n            result.willDrawPage.push(options.willDrawPage);\n        if (options.didDrawPage)\n            result.didDrawPage.push(options.didDrawPage);\n    }\n    return result;\n}\nfunction parseSettings(doc, options) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n    var margin = parseSpacing(options.margin, 40 / doc.scaleFactor());\n    var startY = (_a = getStartY(doc, options.startY)) !== null && _a !== void 0 ? _a : margin.top;\n    var showFoot;\n    if (options.showFoot === true) {\n        showFoot = 'everyPage';\n    }\n    else if (options.showFoot === false) {\n        showFoot = 'never';\n    }\n    else {\n        showFoot = (_b = options.showFoot) !== null && _b !== void 0 ? _b : 'everyPage';\n    }\n    var showHead;\n    if (options.showHead === true) {\n        showHead = 'everyPage';\n    }\n    else if (options.showHead === false) {\n        showHead = 'never';\n    }\n    else {\n        showHead = (_c = options.showHead) !== null && _c !== void 0 ? _c : 'everyPage';\n    }\n    var useCss = (_d = options.useCss) !== null && _d !== void 0 ? _d : false;\n    var theme = options.theme || (useCss ? 'plain' : 'striped');\n    var horizontalPageBreak = !!options.horizontalPageBreak;\n    var horizontalPageBreakRepeat = (_e = options.horizontalPageBreakRepeat) !== null && _e !== void 0 ? _e : null;\n    return {\n        includeHiddenHtml: (_f = options.includeHiddenHtml) !== null && _f !== void 0 ? _f : false,\n        useCss: useCss,\n        theme: theme,\n        startY: startY,\n        margin: margin,\n        pageBreak: (_g = options.pageBreak) !== null && _g !== void 0 ? _g : 'auto',\n        rowPageBreak: (_h = options.rowPageBreak) !== null && _h !== void 0 ? _h : 'auto',\n        tableWidth: (_j = options.tableWidth) !== null && _j !== void 0 ? _j : 'auto',\n        showHead: showHead,\n        showFoot: showFoot,\n        tableLineWidth: (_k = options.tableLineWidth) !== null && _k !== void 0 ? _k : 0,\n        tableLineColor: (_l = options.tableLineColor) !== null && _l !== void 0 ? _l : 200,\n        horizontalPageBreak: horizontalPageBreak,\n        horizontalPageBreakRepeat: horizontalPageBreakRepeat,\n        horizontalPageBreakBehaviour: (_m = options.horizontalPageBreakBehaviour) !== null && _m !== void 0 ? _m : 'afterAllRows',\n    };\n}\nfunction getStartY(doc, userStartY) {\n    var previous = doc.getLastAutoTable();\n    var sf = doc.scaleFactor();\n    var currentPage = doc.pageNumber();\n    var isSamePageAsPreviousTable = false;\n    if (previous && previous.startPageNumber) {\n        var endingPage = previous.startPageNumber + previous.pageNumber - 1;\n        isSamePageAsPreviousTable = endingPage === currentPage;\n    }\n    if (typeof userStartY === 'number') {\n        return userStartY;\n    }\n    else if (userStartY == null || userStartY === false) {\n        if (isSamePageAsPreviousTable && (previous === null || previous === void 0 ? void 0 : previous.finalY) != null) {\n            // Some users had issues with overlapping tables when they used multiple\n            // tables without setting startY so setting it here to a sensible default.\n            return previous.finalY + 20 / sf;\n        }\n    }\n    return null;\n}\nfunction parseContent$1(doc, options, window) {\n    var head = options.head || [];\n    var body = options.body || [];\n    var foot = options.foot || [];\n    if (options.html) {\n        var hidden = options.includeHiddenHtml;\n        if (window) {\n            var htmlContent = parseHtml(doc, options.html, window, hidden, options.useCss) || {};\n            head = htmlContent.head || head;\n            body = htmlContent.body || head;\n            foot = htmlContent.foot || head;\n        }\n        else {\n            console.error('Cannot parse html in non browser environment');\n        }\n    }\n    var columns = options.columns || parseColumns(head, body, foot);\n    return { columns: columns, head: head, body: body, foot: foot };\n}\nfunction parseColumns(head, body, foot) {\n    var firstRow = head[0] || body[0] || foot[0] || [];\n    var result = [];\n    Object.keys(firstRow)\n        .filter(function (key) { return key !== '_element'; })\n        .forEach(function (key) {\n        var colSpan = 1;\n        var input;\n        if (Array.isArray(firstRow)) {\n            input = firstRow[parseInt(key)];\n        }\n        else {\n            input = firstRow[key];\n        }\n        if (typeof input === 'object' && !Array.isArray(input)) {\n            colSpan = (input === null || input === void 0 ? void 0 : input.colSpan) || 1;\n        }\n        for (var i = 0; i < colSpan; i++) {\n            var id = void 0;\n            if (Array.isArray(firstRow)) {\n                id = result.length;\n            }\n            else {\n                id = key + (i > 0 ? \"_\".concat(i) : '');\n            }\n            var rowResult = { dataKey: id };\n            result.push(rowResult);\n        }\n    });\n    return result;\n}\n\nvar HookData = /** @class */ (function () {\n    function HookData(doc, table, cursor) {\n        this.table = table;\n        this.pageNumber = table.pageNumber;\n        this.settings = table.settings;\n        this.cursor = cursor;\n        this.doc = doc.getDocument();\n    }\n    return HookData;\n}());\nvar CellHookData = /** @class */ (function (_super) {\n    __extends(CellHookData, _super);\n    function CellHookData(doc, table, cell, row, column, cursor) {\n        var _this = _super.call(this, doc, table, cursor) || this;\n        _this.cell = cell;\n        _this.row = row;\n        _this.column = column;\n        _this.section = row.section;\n        return _this;\n    }\n    return CellHookData;\n}(HookData));\n\nvar Table = /** @class */ (function () {\n    function Table(input, content) {\n        this.pageNumber = 1;\n        this.id = input.id;\n        this.settings = input.settings;\n        this.styles = input.styles;\n        this.hooks = input.hooks;\n        this.columns = content.columns;\n        this.head = content.head;\n        this.body = content.body;\n        this.foot = content.foot;\n    }\n    Table.prototype.getHeadHeight = function (columns) {\n        return this.head.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\n    };\n    Table.prototype.getFootHeight = function (columns) {\n        return this.foot.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\n    };\n    Table.prototype.allRows = function () {\n        return this.head.concat(this.body).concat(this.foot);\n    };\n    Table.prototype.callCellHooks = function (doc, handlers, cell, row, column, cursor) {\n        for (var _i = 0, handlers_1 = handlers; _i < handlers_1.length; _i++) {\n            var handler = handlers_1[_i];\n            var data = new CellHookData(doc, this, cell, row, column, cursor);\n            var result = handler(data) === false;\n            // Make sure text is always string[] since user can assign string\n            cell.text = Array.isArray(cell.text) ? cell.text : [cell.text];\n            if (result) {\n                return false;\n            }\n        }\n        return true;\n    };\n    Table.prototype.callEndPageHooks = function (doc, cursor) {\n        doc.applyStyles(doc.userStyles);\n        for (var _i = 0, _a = this.hooks.didDrawPage; _i < _a.length; _i++) {\n            var handler = _a[_i];\n            handler(new HookData(doc, this, cursor));\n        }\n    };\n    Table.prototype.callWillDrawPageHooks = function (doc, cursor) {\n        for (var _i = 0, _a = this.hooks.willDrawPage; _i < _a.length; _i++) {\n            var handler = _a[_i];\n            handler(new HookData(doc, this, cursor));\n        }\n    };\n    Table.prototype.getWidth = function (pageWidth) {\n        if (typeof this.settings.tableWidth === 'number') {\n            return this.settings.tableWidth;\n        }\n        else if (this.settings.tableWidth === 'wrap') {\n            var wrappedWidth = this.columns.reduce(function (total, col) { return total + col.wrappedWidth; }, 0);\n            return wrappedWidth;\n        }\n        else {\n            var margin = this.settings.margin;\n            return pageWidth - margin.left - margin.right;\n        }\n    };\n    return Table;\n}());\nvar Row = /** @class */ (function () {\n    function Row(raw, index, section, cells, spansMultiplePages) {\n        if (spansMultiplePages === void 0) { spansMultiplePages = false; }\n        this.height = 0;\n        this.raw = raw;\n        if (raw instanceof HtmlRowInput) {\n            this.raw = raw._element;\n            this.element = raw._element;\n        }\n        this.index = index;\n        this.section = section;\n        this.cells = cells;\n        this.spansMultiplePages = spansMultiplePages;\n    }\n    Row.prototype.getMaxCellHeight = function (columns) {\n        var _this = this;\n        return columns.reduce(function (acc, column) { var _a; return Math.max(acc, ((_a = _this.cells[column.index]) === null || _a === void 0 ? void 0 : _a.height) || 0); }, 0);\n    };\n    Row.prototype.hasRowSpan = function (columns) {\n        var _this = this;\n        return (columns.filter(function (column) {\n            var cell = _this.cells[column.index];\n            if (!cell)\n                return false;\n            return cell.rowSpan > 1;\n        }).length > 0);\n    };\n    Row.prototype.canEntireRowFit = function (height, columns) {\n        return this.getMaxCellHeight(columns) <= height;\n    };\n    Row.prototype.getMinimumRowHeight = function (columns, doc) {\n        var _this = this;\n        return columns.reduce(function (acc, column) {\n            var cell = _this.cells[column.index];\n            if (!cell)\n                return 0;\n            var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n            var vPadding = cell.padding('vertical');\n            var oneRowHeight = vPadding + lineHeight;\n            return oneRowHeight > acc ? oneRowHeight : acc;\n        }, 0);\n    };\n    return Row;\n}());\nvar Cell = /** @class */ (function () {\n    function Cell(raw, styles, section) {\n        var _a;\n        this.contentHeight = 0;\n        this.contentWidth = 0;\n        this.wrappedWidth = 0;\n        this.minReadableWidth = 0;\n        this.minWidth = 0;\n        this.width = 0;\n        this.height = 0;\n        this.x = 0;\n        this.y = 0;\n        this.styles = styles;\n        this.section = section;\n        this.raw = raw;\n        var content = raw;\n        if (raw != null && typeof raw === 'object' && !Array.isArray(raw)) {\n            this.rowSpan = raw.rowSpan || 1;\n            this.colSpan = raw.colSpan || 1;\n            content = (_a = raw.content) !== null && _a !== void 0 ? _a : raw;\n            if (raw._element) {\n                this.raw = raw._element;\n            }\n        }\n        else {\n            this.rowSpan = 1;\n            this.colSpan = 1;\n        }\n        // Stringify 0 and false, but not undefined or null\n        var text = content != null ? '' + content : '';\n        var splitRegex = /\\r\\n|\\r|\\n/g;\n        this.text = text.split(splitRegex);\n    }\n    Cell.prototype.getTextPos = function () {\n        var y;\n        if (this.styles.valign === 'top') {\n            y = this.y + this.padding('top');\n        }\n        else if (this.styles.valign === 'bottom') {\n            y = this.y + this.height - this.padding('bottom');\n        }\n        else {\n            var netHeight = this.height - this.padding('vertical');\n            y = this.y + netHeight / 2 + this.padding('top');\n        }\n        var x;\n        if (this.styles.halign === 'right') {\n            x = this.x + this.width - this.padding('right');\n        }\n        else if (this.styles.halign === 'center') {\n            var netWidth = this.width - this.padding('horizontal');\n            x = this.x + netWidth / 2 + this.padding('left');\n        }\n        else {\n            x = this.x + this.padding('left');\n        }\n        return { x: x, y: y };\n    };\n    // TODO (v4): replace parameters with only (lineHeight)\n    Cell.prototype.getContentHeight = function (scaleFactor, lineHeightFactor) {\n        if (lineHeightFactor === void 0) { lineHeightFactor = 1.15; }\n        var lineCount = Array.isArray(this.text) ? this.text.length : 1;\n        var lineHeight = (this.styles.fontSize / scaleFactor) * lineHeightFactor;\n        var height = lineCount * lineHeight + this.padding('vertical');\n        return Math.max(height, this.styles.minCellHeight);\n    };\n    Cell.prototype.padding = function (name) {\n        var padding = parseSpacing(this.styles.cellPadding, 0);\n        if (name === 'vertical') {\n            return padding.top + padding.bottom;\n        }\n        else if (name === 'horizontal') {\n            return padding.left + padding.right;\n        }\n        else {\n            return padding[name];\n        }\n    };\n    return Cell;\n}());\nvar Column = /** @class */ (function () {\n    function Column(dataKey, raw, index) {\n        this.wrappedWidth = 0;\n        this.minReadableWidth = 0;\n        this.minWidth = 0;\n        this.width = 0;\n        this.dataKey = dataKey;\n        this.raw = raw;\n        this.index = index;\n    }\n    Column.prototype.getMaxCustomCellWidth = function (table) {\n        var max = 0;\n        for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n            var row = _a[_i];\n            var cell = row.cells[this.index];\n            if (cell && typeof cell.styles.cellWidth === 'number') {\n                max = Math.max(max, cell.styles.cellWidth);\n            }\n        }\n        return max;\n    };\n    return Column;\n}());\n\n/**\n * Calculate the column widths\n */\nfunction calculateWidths(doc, table) {\n    calculate(doc, table);\n    var resizableColumns = [];\n    var initialTableWidth = 0;\n    table.columns.forEach(function (column) {\n        var customWidth = column.getMaxCustomCellWidth(table);\n        if (customWidth) {\n            // final column width\n            column.width = customWidth;\n        }\n        else {\n            // initial column width (will be resized)\n            column.width = column.wrappedWidth;\n            resizableColumns.push(column);\n        }\n        initialTableWidth += column.width;\n    });\n    // width difference that needs to be distributed\n    var resizeWidth = table.getWidth(doc.pageSize().width) - initialTableWidth;\n    // first resize attempt: with respect to minReadableWidth and minWidth\n    if (resizeWidth) {\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n            return Math.max(column.minReadableWidth, column.minWidth);\n        });\n    }\n    // second resize attempt: ignore minReadableWidth but respect minWidth\n    if (resizeWidth) {\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) { return column.minWidth; });\n    }\n    resizeWidth = Math.abs(resizeWidth);\n    if (!table.settings.horizontalPageBreak &&\n        resizeWidth > 0.1 / doc.scaleFactor()) {\n        // Table can't get smaller due to custom-width or minWidth restrictions\n        // We can't really do much here. Up to user to for example\n        // reduce font size, increase page size or remove custom cell widths\n        // to allow more columns to be reduced in size\n        resizeWidth = resizeWidth < 1 ? resizeWidth : Math.round(resizeWidth);\n        console.warn(\"Of the table content, \".concat(resizeWidth, \" units width could not fit page\"));\n    }\n    applyColSpans(table);\n    fitContent(table, doc);\n    applyRowSpans(table);\n}\nfunction calculate(doc, table) {\n    var sf = doc.scaleFactor();\n    var horizontalPageBreak = table.settings.horizontalPageBreak;\n    var availablePageWidth = getPageAvailableWidth(doc, table);\n    table.allRows().forEach(function (row) {\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var cell = row.cells[column.index];\n            if (!cell)\n                continue;\n            var hooks = table.hooks.didParseCell;\n            table.callCellHooks(doc, hooks, cell, row, column, null);\n            var padding = cell.padding('horizontal');\n            cell.contentWidth = getStringWidth(cell.text, cell.styles, doc) + padding;\n            // Using [^\\S\\u00A0] instead of \\s ensures that we split the text on all\n            // whitespace except non-breaking spaces (\\u00A0). We need to preserve\n            // them in the split process to ensure correct word separation and width\n            // calculation.\n            var longestWordWidth = getStringWidth(cell.text.join(' ').split(/[^\\S\\u00A0]+/), cell.styles, doc);\n            cell.minReadableWidth = longestWordWidth + cell.padding('horizontal');\n            if (typeof cell.styles.cellWidth === 'number') {\n                cell.minWidth = cell.styles.cellWidth;\n                cell.wrappedWidth = cell.styles.cellWidth;\n            }\n            else if (cell.styles.cellWidth === 'wrap' ||\n                horizontalPageBreak === true) {\n                // cell width should not be more than available page width\n                if (cell.contentWidth > availablePageWidth) {\n                    cell.minWidth = availablePageWidth;\n                    cell.wrappedWidth = availablePageWidth;\n                }\n                else {\n                    cell.minWidth = cell.contentWidth;\n                    cell.wrappedWidth = cell.contentWidth;\n                }\n            }\n            else {\n                // auto\n                var defaultMinWidth = 10 / sf;\n                cell.minWidth = cell.styles.minCellWidth || defaultMinWidth;\n                cell.wrappedWidth = cell.contentWidth;\n                if (cell.minWidth > cell.wrappedWidth) {\n                    cell.wrappedWidth = cell.minWidth;\n                }\n            }\n        }\n    });\n    table.allRows().forEach(function (row) {\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var cell = row.cells[column.index];\n            // For now we ignore the minWidth and wrappedWidth of colspan cells when calculating colspan widths.\n            // Could probably be improved upon however.\n            if (cell && cell.colSpan === 1) {\n                column.wrappedWidth = Math.max(column.wrappedWidth, cell.wrappedWidth);\n                column.minWidth = Math.max(column.minWidth, cell.minWidth);\n                column.minReadableWidth = Math.max(column.minReadableWidth, cell.minReadableWidth);\n            }\n            else {\n                // Respect cellWidth set in columnStyles even if there is no cells for this column\n                // or if the column only have colspan cells. Since the width of colspan cells\n                // does not affect the width of columns, setting columnStyles cellWidth enables the\n                // user to at least do it manually.\n                // Note that this is not perfect for now since for example row and table styles are\n                // not accounted for\n                var columnStyles = table.styles.columnStyles[column.dataKey] ||\n                    table.styles.columnStyles[column.index] ||\n                    {};\n                var cellWidth = columnStyles.cellWidth || columnStyles.minCellWidth;\n                if (cellWidth && typeof cellWidth === 'number') {\n                    column.minWidth = cellWidth;\n                    column.wrappedWidth = cellWidth;\n                }\n            }\n            if (cell) {\n                // Make sure all columns get at least min width even though width calculations are not based on them\n                if (cell.colSpan > 1 && !column.minWidth) {\n                    column.minWidth = cell.minWidth;\n                }\n                if (cell.colSpan > 1 && !column.wrappedWidth) {\n                    column.wrappedWidth = cell.minWidth;\n                }\n            }\n        }\n    });\n}\n/**\n * Distribute resizeWidth on passed resizable columns\n */\nfunction resizeColumns(columns, resizeWidth, getMinWidth) {\n    var initialResizeWidth = resizeWidth;\n    var sumWrappedWidth = columns.reduce(function (acc, column) { return acc + column.wrappedWidth; }, 0);\n    for (var i = 0; i < columns.length; i++) {\n        var column = columns[i];\n        var ratio = column.wrappedWidth / sumWrappedWidth;\n        var suggestedChange = initialResizeWidth * ratio;\n        var suggestedWidth = column.width + suggestedChange;\n        var minWidth = getMinWidth(column);\n        var newWidth = suggestedWidth < minWidth ? minWidth : suggestedWidth;\n        resizeWidth -= newWidth - column.width;\n        column.width = newWidth;\n    }\n    resizeWidth = Math.round(resizeWidth * 1e10) / 1e10;\n    // Run the resizer again if there's remaining width needs\n    // to be distributed and there're columns that can be resized\n    if (resizeWidth) {\n        var resizableColumns = columns.filter(function (column) {\n            return resizeWidth < 0\n                ? column.width > getMinWidth(column) // check if column can shrink\n                : true; // check if column can grow\n        });\n        if (resizableColumns.length) {\n            resizeWidth = resizeColumns(resizableColumns, resizeWidth, getMinWidth);\n        }\n    }\n    return resizeWidth;\n}\nfunction applyRowSpans(table) {\n    var rowSpanCells = {};\n    var colRowSpansLeft = 1;\n    var all = table.allRows();\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n        var row = all[rowIndex];\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var data = rowSpanCells[column.index];\n            if (colRowSpansLeft > 1) {\n                colRowSpansLeft--;\n                delete row.cells[column.index];\n            }\n            else if (data) {\n                data.cell.height += row.height;\n                colRowSpansLeft = data.cell.colSpan;\n                delete row.cells[column.index];\n                data.left--;\n                if (data.left <= 1) {\n                    delete rowSpanCells[column.index];\n                }\n            }\n            else {\n                var cell = row.cells[column.index];\n                if (!cell) {\n                    continue;\n                }\n                cell.height = row.height;\n                if (cell.rowSpan > 1) {\n                    var remaining = all.length - rowIndex;\n                    var left = cell.rowSpan > remaining ? remaining : cell.rowSpan;\n                    rowSpanCells[column.index] = { cell: cell, left: left, row: row };\n                }\n            }\n        }\n    }\n}\nfunction applyColSpans(table) {\n    var all = table.allRows();\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n        var row = all[rowIndex];\n        var colSpanCell = null;\n        var combinedColSpanWidth = 0;\n        var colSpansLeft = 0;\n        for (var columnIndex = 0; columnIndex < table.columns.length; columnIndex++) {\n            var column = table.columns[columnIndex];\n            // Width and colspan\n            colSpansLeft -= 1;\n            if (colSpansLeft > 1 && table.columns[columnIndex + 1]) {\n                combinedColSpanWidth += column.width;\n                delete row.cells[column.index];\n            }\n            else if (colSpanCell) {\n                var cell = colSpanCell;\n                delete row.cells[column.index];\n                colSpanCell = null;\n                cell.width = column.width + combinedColSpanWidth;\n            }\n            else {\n                var cell = row.cells[column.index];\n                if (!cell)\n                    continue;\n                colSpansLeft = cell.colSpan;\n                combinedColSpanWidth = 0;\n                if (cell.colSpan > 1) {\n                    colSpanCell = cell;\n                    combinedColSpanWidth += column.width;\n                    continue;\n                }\n                cell.width = column.width + combinedColSpanWidth;\n            }\n        }\n    }\n}\nfunction fitContent(table, doc) {\n    var rowSpanHeight = { count: 0, height: 0 };\n    for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n        var row = _a[_i];\n        for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n            var column = _c[_b];\n            var cell = row.cells[column.index];\n            if (!cell)\n                continue;\n            doc.applyStyles(cell.styles, true);\n            var textSpace = cell.width - cell.padding('horizontal');\n            if (cell.styles.overflow === 'linebreak') {\n                // Add one pt to textSpace to fix rounding error\n                cell.text = doc.splitTextToSize(cell.text, textSpace + 1 / doc.scaleFactor(), { fontSize: cell.styles.fontSize });\n            }\n            else if (cell.styles.overflow === 'ellipsize') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '...');\n            }\n            else if (cell.styles.overflow === 'hidden') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '');\n            }\n            else if (typeof cell.styles.overflow === 'function') {\n                var result = cell.styles.overflow(cell.text, textSpace);\n                if (typeof result === 'string') {\n                    cell.text = [result];\n                }\n                else {\n                    cell.text = result;\n                }\n            }\n            cell.contentHeight = cell.getContentHeight(doc.scaleFactor(), doc.getLineHeightFactor());\n            var realContentHeight = cell.contentHeight / cell.rowSpan;\n            if (cell.rowSpan > 1 &&\n                rowSpanHeight.count * rowSpanHeight.height <\n                    realContentHeight * cell.rowSpan) {\n                rowSpanHeight = { height: realContentHeight, count: cell.rowSpan };\n            }\n            else if (rowSpanHeight && rowSpanHeight.count > 0) {\n                if (rowSpanHeight.height > realContentHeight) {\n                    realContentHeight = rowSpanHeight.height;\n                }\n            }\n            if (realContentHeight > row.height) {\n                row.height = realContentHeight;\n            }\n        }\n        rowSpanHeight.count--;\n    }\n}\nfunction ellipsize(text, width, styles, doc, overflow) {\n    return text.map(function (str) { return ellipsizeStr(str, width, styles, doc, overflow); });\n}\nfunction ellipsizeStr(text, width, styles, doc, overflow) {\n    var precision = 10000 * doc.scaleFactor();\n    width = Math.ceil(width * precision) / precision;\n    if (width >= getStringWidth(text, styles, doc)) {\n        return text;\n    }\n    while (width < getStringWidth(text + overflow, styles, doc)) {\n        if (text.length <= 1) {\n            break;\n        }\n        text = text.substring(0, text.length - 1);\n    }\n    return text.trim() + overflow;\n}\n\nfunction createTable(jsPDFDoc, input) {\n    var doc = new DocHandler(jsPDFDoc);\n    var content = parseContent(input, doc.scaleFactor());\n    var table = new Table(input, content);\n    calculateWidths(doc, table);\n    doc.applyStyles(doc.userStyles);\n    return table;\n}\nfunction parseContent(input, sf) {\n    var content = input.content;\n    var columns = createColumns(content.columns);\n    // If no head or foot is set, try generating it with content from columns\n    if (content.head.length === 0) {\n        var sectionRow = generateSectionRow(columns, 'head');\n        if (sectionRow)\n            content.head.push(sectionRow);\n    }\n    if (content.foot.length === 0) {\n        var sectionRow = generateSectionRow(columns, 'foot');\n        if (sectionRow)\n            content.foot.push(sectionRow);\n    }\n    var theme = input.settings.theme;\n    var styles = input.styles;\n    return {\n        columns: columns,\n        head: parseSection('head', content.head, columns, styles, theme, sf),\n        body: parseSection('body', content.body, columns, styles, theme, sf),\n        foot: parseSection('foot', content.foot, columns, styles, theme, sf),\n    };\n}\nfunction parseSection(sectionName, sectionRows, columns, styleProps, theme, scaleFactor) {\n    var rowSpansLeftForColumn = {};\n    var result = sectionRows.map(function (rawRow, rowIndex) {\n        var skippedRowForRowSpans = 0;\n        var cells = {};\n        var colSpansAdded = 0;\n        var columnSpansLeft = 0;\n        for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n            var column = columns_1[_i];\n            if (rowSpansLeftForColumn[column.index] == null ||\n                rowSpansLeftForColumn[column.index].left === 0) {\n                if (columnSpansLeft === 0) {\n                    var rawCell = void 0;\n                    if (Array.isArray(rawRow)) {\n                        rawCell =\n                            rawRow[column.index - colSpansAdded - skippedRowForRowSpans];\n                    }\n                    else {\n                        rawCell = rawRow[column.dataKey];\n                    }\n                    var cellInputStyles = {};\n                    if (typeof rawCell === 'object' && !Array.isArray(rawCell)) {\n                        cellInputStyles = (rawCell === null || rawCell === void 0 ? void 0 : rawCell.styles) || {};\n                    }\n                    var styles = cellStyles(sectionName, column, rowIndex, theme, styleProps, scaleFactor, cellInputStyles);\n                    var cell = new Cell(rawCell, styles, sectionName);\n                    // dataKey is not used internally no more but keep for\n                    // backwards compat in hooks\n                    cells[column.dataKey] = cell;\n                    cells[column.index] = cell;\n                    columnSpansLeft = cell.colSpan - 1;\n                    rowSpansLeftForColumn[column.index] = {\n                        left: cell.rowSpan - 1,\n                        times: columnSpansLeft,\n                    };\n                }\n                else {\n                    columnSpansLeft--;\n                    colSpansAdded++;\n                }\n            }\n            else {\n                rowSpansLeftForColumn[column.index].left--;\n                columnSpansLeft = rowSpansLeftForColumn[column.index].times;\n                skippedRowForRowSpans++;\n            }\n        }\n        return new Row(rawRow, rowIndex, sectionName, cells);\n    });\n    return result;\n}\nfunction generateSectionRow(columns, section) {\n    var sectionRow = {};\n    columns.forEach(function (col) {\n        if (col.raw != null) {\n            var title = getSectionTitle(section, col.raw);\n            if (title != null)\n                sectionRow[col.dataKey] = title;\n        }\n    });\n    return Object.keys(sectionRow).length > 0 ? sectionRow : null;\n}\nfunction getSectionTitle(section, column) {\n    if (section === 'head') {\n        if (typeof column === 'object') {\n            return column.header || null;\n        }\n        else if (typeof column === 'string' || typeof column === 'number') {\n            return column;\n        }\n    }\n    else if (section === 'foot' && typeof column === 'object') {\n        return column.footer;\n    }\n    return null;\n}\nfunction createColumns(columns) {\n    return columns.map(function (input, index) {\n        var _a;\n        var key;\n        if (typeof input === 'object') {\n            key = (_a = input.dataKey) !== null && _a !== void 0 ? _a : index;\n        }\n        else {\n            key = index;\n        }\n        return new Column(key, input, index);\n    });\n}\nfunction cellStyles(sectionName, column, rowIndex, themeName, styles, scaleFactor, cellInputStyles) {\n    var theme = getTheme(themeName);\n    var sectionStyles;\n    if (sectionName === 'head') {\n        sectionStyles = styles.headStyles;\n    }\n    else if (sectionName === 'body') {\n        sectionStyles = styles.bodyStyles;\n    }\n    else if (sectionName === 'foot') {\n        sectionStyles = styles.footStyles;\n    }\n    var otherStyles = assign({}, theme.table, theme[sectionName], styles.styles, sectionStyles);\n    var columnStyles = styles.columnStyles[column.dataKey] ||\n        styles.columnStyles[column.index] ||\n        {};\n    var colStyles = sectionName === 'body' ? columnStyles : {};\n    var rowStyles = sectionName === 'body' && rowIndex % 2 === 0\n        ? assign({}, theme.alternateRow, styles.alternateRowStyles)\n        : {};\n    var defaultStyle = defaultStyles(scaleFactor);\n    var themeStyles = assign({}, defaultStyle, otherStyles, rowStyles, colStyles);\n    return assign(themeStyles, cellInputStyles);\n}\n\n// get columns can be fit into page\nfunction getColumnsCanFitInPage(doc, table, config) {\n    var _a;\n    if (config === void 0) { config = {}; }\n    // Get page width\n    var remainingWidth = getPageAvailableWidth(doc, table);\n    // Get column data key to repeat\n    var repeatColumnsMap = new Map();\n    var colIndexes = [];\n    var columns = [];\n    var horizontalPageBreakRepeat = [];\n    if (Array.isArray(table.settings.horizontalPageBreakRepeat)) {\n        horizontalPageBreakRepeat = table.settings.horizontalPageBreakRepeat;\n        // It can be a single value of type string or number (even number: 0)\n    }\n    else if (typeof table.settings.horizontalPageBreakRepeat === 'string' ||\n        typeof table.settings.horizontalPageBreakRepeat === 'number') {\n        horizontalPageBreakRepeat = [table.settings.horizontalPageBreakRepeat];\n    }\n    // Code to repeat the given column in split pages\n    horizontalPageBreakRepeat.forEach(function (field) {\n        var col = table.columns.find(function (item) { return item.dataKey === field || item.index === field; });\n        if (col && !repeatColumnsMap.has(col.index)) {\n            repeatColumnsMap.set(col.index, true);\n            colIndexes.push(col.index);\n            columns.push(table.columns[col.index]);\n            remainingWidth -= col.wrappedWidth;\n        }\n    });\n    var first = true;\n    var i = (_a = config === null || config === void 0 ? void 0 : config.start) !== null && _a !== void 0 ? _a : 0; // make sure couter is initiated outside the loop\n    while (i < table.columns.length) {\n        // Prevent duplicates\n        if (repeatColumnsMap.has(i)) {\n            i++;\n            continue;\n        }\n        var colWidth = table.columns[i].wrappedWidth;\n        // Take at least one column even if it doesn't fit\n        if (first || remainingWidth >= colWidth) {\n            first = false;\n            colIndexes.push(i);\n            columns.push(table.columns[i]);\n            remainingWidth -= colWidth;\n        }\n        else {\n            break;\n        }\n        i++;\n    }\n    return { colIndexes: colIndexes, columns: columns, lastIndex: i - 1 };\n}\nfunction calculateAllColumnsCanFitInPage(doc, table) {\n    var allResults = [];\n    for (var i = 0; i < table.columns.length; i++) {\n        var result = getColumnsCanFitInPage(doc, table, { start: i });\n        if (result.columns.length) {\n            allResults.push(result);\n            i = result.lastIndex;\n        }\n    }\n    return allResults;\n}\n\nfunction drawTable(jsPDFDoc, table) {\n    var settings = table.settings;\n    var startY = settings.startY;\n    var margin = settings.margin;\n    var cursor = { x: margin.left, y: startY };\n    var sectionsHeight = table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n    var minTableBottomPos = startY + margin.bottom + sectionsHeight;\n    if (settings.pageBreak === 'avoid') {\n        var rows = table.body;\n        var tableHeight = rows.reduce(function (acc, row) { return acc + row.height; }, 0);\n        minTableBottomPos += tableHeight;\n    }\n    var doc = new DocHandler(jsPDFDoc);\n    if (settings.pageBreak === 'always' ||\n        (settings.startY != null && minTableBottomPos > doc.pageSize().height)) {\n        nextPage(doc);\n        cursor.y = margin.top;\n    }\n    table.callWillDrawPageHooks(doc, cursor);\n    var startPos = assign({}, cursor);\n    table.startPageNumber = doc.pageNumber();\n    if (settings.horizontalPageBreak) {\n        // managed flow for split columns\n        printTableWithHorizontalPageBreak(doc, table, startPos, cursor);\n    }\n    else {\n        // normal flow\n        doc.applyStyles(doc.userStyles);\n        if (settings.showHead === 'firstPage' ||\n            settings.showHead === 'everyPage') {\n            table.head.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n            });\n        }\n        doc.applyStyles(doc.userStyles);\n        table.body.forEach(function (row, index) {\n            var isLastRow = index === table.body.length - 1;\n            printFullRow(doc, table, row, isLastRow, startPos, cursor, table.columns);\n        });\n        doc.applyStyles(doc.userStyles);\n        if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n            table.foot.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n            });\n        }\n    }\n    addTableBorder(doc, table, startPos, cursor);\n    table.callEndPageHooks(doc, cursor);\n    table.finalY = cursor.y;\n    jsPDFDoc.lastAutoTable = table;\n    doc.applyStyles(doc.userStyles);\n}\nfunction printTableWithHorizontalPageBreak(doc, table, startPos, cursor) {\n    // calculate width of columns and render only those which can fit into page\n    var allColumnsCanFitResult = calculateAllColumnsCanFitInPage(doc, table);\n    var settings = table.settings;\n    if (settings.horizontalPageBreakBehaviour === 'afterAllRows') {\n        allColumnsCanFitResult.forEach(function (colsAndIndexes, index) {\n            doc.applyStyles(doc.userStyles);\n            // add page to print next columns in new page\n            if (index > 0) {\n                // When adding a page here, make sure not to print the footers\n                // because they were already printed before on this same loop\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n            }\n            else {\n                // print head for selected columns\n                printHead(doc, table, cursor, colsAndIndexes.columns);\n            }\n            // print body & footer for selected columns\n            printBody(doc, table, startPos, cursor, colsAndIndexes.columns);\n            printFoot(doc, table, cursor, colsAndIndexes.columns);\n        });\n    }\n    else {\n        var lastRowIndexOfLastPage_1 = -1;\n        var firstColumnsToFitResult = allColumnsCanFitResult[0];\n        var _loop_1 = function () {\n            // Print the first columns, taking note of the last row printed\n            var lastPrintedRowIndex = lastRowIndexOfLastPage_1;\n            if (firstColumnsToFitResult) {\n                doc.applyStyles(doc.userStyles);\n                var firstColumnsToFit = firstColumnsToFitResult.columns;\n                if (lastRowIndexOfLastPage_1 >= 0) {\n                    // When adding a page here, make sure not to print the footers\n                    // because they were already printed before on this same loop\n                    addPage(doc, table, startPos, cursor, firstColumnsToFit, true);\n                }\n                else {\n                    printHead(doc, table, cursor, firstColumnsToFit);\n                }\n                lastPrintedRowIndex = printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, firstColumnsToFit);\n                printFoot(doc, table, cursor, firstColumnsToFit);\n            }\n            // Check how many rows were printed, so that the next columns would not print more rows than that\n            var maxNumberOfRows = lastPrintedRowIndex - lastRowIndexOfLastPage_1;\n            // Print the next columns, never exceding maxNumberOfRows\n            allColumnsCanFitResult.slice(1).forEach(function (colsAndIndexes) {\n                doc.applyStyles(doc.userStyles);\n                // When adding a page here, make sure not to print the footers\n                // because they were already printed before on this same loop\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n                printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, colsAndIndexes.columns, maxNumberOfRows);\n                printFoot(doc, table, cursor, colsAndIndexes.columns);\n            });\n            lastRowIndexOfLastPage_1 = lastPrintedRowIndex;\n        };\n        while (lastRowIndexOfLastPage_1 < table.body.length - 1) {\n            _loop_1();\n        }\n    }\n}\nfunction printHead(doc, table, cursor, columns) {\n    var settings = table.settings;\n    doc.applyStyles(doc.userStyles);\n    if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n}\nfunction printBody(doc, table, startPos, cursor, columns) {\n    doc.applyStyles(doc.userStyles);\n    table.body.forEach(function (row, index) {\n        var isLastRow = index === table.body.length - 1;\n        printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n    });\n}\nfunction printBodyWithoutPageBreaks(doc, table, startRowIndex, cursor, columns, maxNumberOfRows) {\n    doc.applyStyles(doc.userStyles);\n    maxNumberOfRows = maxNumberOfRows !== null && maxNumberOfRows !== void 0 ? maxNumberOfRows : table.body.length;\n    var endRowIndex = Math.min(startRowIndex + maxNumberOfRows, table.body.length);\n    var lastPrintedRowIndex = -1;\n    table.body.slice(startRowIndex, endRowIndex).forEach(function (row, index) {\n        var isLastRow = startRowIndex + index === table.body.length - 1;\n        var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n        if (row.canEntireRowFit(remainingSpace, columns)) {\n            printRow(doc, table, row, cursor, columns);\n            lastPrintedRowIndex = startRowIndex + index;\n        }\n    });\n    return lastPrintedRowIndex;\n}\nfunction printFoot(doc, table, cursor, columns) {\n    var settings = table.settings;\n    doc.applyStyles(doc.userStyles);\n    if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n}\nfunction getRemainingLineCount(cell, remainingPageSpace, doc) {\n    var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n    var vPadding = cell.padding('vertical');\n    var remainingLines = Math.floor((remainingPageSpace - vPadding) / lineHeight);\n    return Math.max(0, remainingLines);\n}\nfunction modifyRowToFit(row, remainingPageSpace, table, doc) {\n    var cells = {};\n    row.spansMultiplePages = true;\n    row.height = 0;\n    var rowHeight = 0;\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n        var column = _a[_i];\n        var cell = row.cells[column.index];\n        if (!cell)\n            continue;\n        if (!Array.isArray(cell.text)) {\n            cell.text = [cell.text];\n        }\n        var remainderCell = new Cell(cell.raw, cell.styles, cell.section);\n        remainderCell = assign(remainderCell, cell);\n        remainderCell.text = [];\n        var remainingLineCount = getRemainingLineCount(cell, remainingPageSpace, doc);\n        if (cell.text.length > remainingLineCount) {\n            remainderCell.text = cell.text.splice(remainingLineCount, cell.text.length);\n        }\n        var scaleFactor = doc.scaleFactor();\n        var lineHeightFactor = doc.getLineHeightFactor();\n        cell.contentHeight = cell.getContentHeight(scaleFactor, lineHeightFactor);\n        if (cell.contentHeight >= remainingPageSpace) {\n            cell.contentHeight = remainingPageSpace;\n            remainderCell.styles.minCellHeight -= remainingPageSpace;\n        }\n        if (cell.contentHeight > row.height) {\n            row.height = cell.contentHeight;\n        }\n        remainderCell.contentHeight = remainderCell.getContentHeight(scaleFactor, lineHeightFactor);\n        if (remainderCell.contentHeight > rowHeight) {\n            rowHeight = remainderCell.contentHeight;\n        }\n        cells[column.index] = remainderCell;\n    }\n    var remainderRow = new Row(row.raw, -1, row.section, cells, true);\n    remainderRow.height = rowHeight;\n    for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n        var column = _c[_b];\n        var remainderCell = remainderRow.cells[column.index];\n        if (remainderCell) {\n            remainderCell.height = remainderRow.height;\n        }\n        var cell = row.cells[column.index];\n        if (cell) {\n            cell.height = row.height;\n        }\n    }\n    return remainderRow;\n}\nfunction shouldPrintOnCurrentPage(doc, row, remainingPageSpace, table) {\n    var pageHeight = doc.pageSize().height;\n    var margin = table.settings.margin;\n    var marginHeight = margin.top + margin.bottom;\n    var maxRowHeight = pageHeight - marginHeight;\n    if (row.section === 'body') {\n        // Should also take into account that head and foot is not\n        // on every page with some settings\n        maxRowHeight -=\n            table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n    }\n    var minRowHeight = row.getMinimumRowHeight(table.columns, doc);\n    var minRowFits = minRowHeight < remainingPageSpace;\n    if (minRowHeight > maxRowHeight) {\n        console.error(\"Will not be able to print row \".concat(row.index, \" correctly since it's minimum height is larger than page height\"));\n        return true;\n    }\n    if (!minRowFits) {\n        return false;\n    }\n    var rowHasRowSpanCell = row.hasRowSpan(table.columns);\n    var rowHigherThanPage = row.getMaxCellHeight(table.columns) > maxRowHeight;\n    if (rowHigherThanPage) {\n        if (rowHasRowSpanCell) {\n            console.error(\"The content of row \".concat(row.index, \" will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.\"));\n        }\n        return true;\n    }\n    if (rowHasRowSpanCell) {\n        // Currently a new page is required whenever a rowspan row don't fit a page.\n        return false;\n    }\n    if (table.settings.rowPageBreak === 'avoid') {\n        return false;\n    }\n    // In all other cases print the row on current page\n    return true;\n}\nfunction printFullRow(doc, table, row, isLastRow, startPos, cursor, columns) {\n    var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n    if (row.canEntireRowFit(remainingSpace, columns)) {\n        // The row fits in the current page\n        printRow(doc, table, row, cursor, columns);\n    }\n    else if (shouldPrintOnCurrentPage(doc, row, remainingSpace, table)) {\n        // The row gets split in two here, each piece in one page\n        var remainderRow = modifyRowToFit(row, remainingSpace, table, doc);\n        printRow(doc, table, row, cursor, columns);\n        addPage(doc, table, startPos, cursor, columns);\n        printFullRow(doc, table, remainderRow, isLastRow, startPos, cursor, columns);\n    }\n    else {\n        // The row get printed entirelly on the next page\n        addPage(doc, table, startPos, cursor, columns);\n        printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n    }\n}\nfunction printRow(doc, table, row, cursor, columns) {\n    cursor.x = table.settings.margin.left;\n    for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n        var column = columns_1[_i];\n        var cell = row.cells[column.index];\n        if (!cell) {\n            cursor.x += column.width;\n            continue;\n        }\n        doc.applyStyles(cell.styles);\n        cell.x = cursor.x;\n        cell.y = cursor.y;\n        var result = table.callCellHooks(doc, table.hooks.willDrawCell, cell, row, column, cursor);\n        if (result === false) {\n            cursor.x += column.width;\n            continue;\n        }\n        drawCellRect(doc, cell, cursor);\n        var textPos = cell.getTextPos();\n        autoTableText(cell.text, textPos.x, textPos.y, {\n            halign: cell.styles.halign,\n            valign: cell.styles.valign,\n            maxWidth: Math.ceil(cell.width - cell.padding('left') - cell.padding('right')),\n        }, doc.getDocument());\n        table.callCellHooks(doc, table.hooks.didDrawCell, cell, row, column, cursor);\n        cursor.x += column.width;\n    }\n    cursor.y += row.height;\n}\nfunction drawCellRect(doc, cell, cursor) {\n    var cellStyles = cell.styles;\n    // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/774\n    // TODO (v4): better solution?\n    doc.getDocument().setFillColor(doc.getDocument().getFillColor());\n    if (typeof cellStyles.lineWidth === 'number') {\n        // Draw cell background with normal borders\n        var fillStyle = getFillStyle(cellStyles.lineWidth, cellStyles.fillColor);\n        if (fillStyle) {\n            doc.rect(cell.x, cursor.y, cell.width, cell.height, fillStyle);\n        }\n    }\n    else if (typeof cellStyles.lineWidth === 'object') {\n        // Draw cell background\n        if (cellStyles.fillColor) {\n            doc.rect(cell.x, cursor.y, cell.width, cell.height, 'F');\n        }\n        // Draw cell individual borders\n        drawCellBorders(doc, cell, cursor, cellStyles.lineWidth);\n    }\n}\n/**\n * Draw all specified borders. Borders are centered on cell's edge and lengthened\n * to overlap with neighbours to create sharp corners.\n * @param doc\n * @param cell\n * @param cursor\n * @param fillColor\n * @param lineWidth\n */\nfunction drawCellBorders(doc, cell, cursor, lineWidth) {\n    var x1, y1, x2, y2;\n    if (lineWidth.top) {\n        x1 = cursor.x;\n        y1 = cursor.y;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y;\n        if (lineWidth.right) {\n            x2 += 0.5 * lineWidth.right;\n        }\n        if (lineWidth.left) {\n            x1 -= 0.5 * lineWidth.left;\n        }\n        drawLine(lineWidth.top, x1, y1, x2, y2);\n    }\n    if (lineWidth.bottom) {\n        x1 = cursor.x;\n        y1 = cursor.y + cell.height;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.right) {\n            x2 += 0.5 * lineWidth.right;\n        }\n        if (lineWidth.left) {\n            x1 -= 0.5 * lineWidth.left;\n        }\n        drawLine(lineWidth.bottom, x1, y1, x2, y2);\n    }\n    if (lineWidth.left) {\n        x1 = cursor.x;\n        y1 = cursor.y;\n        x2 = cursor.x;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.top) {\n            y1 -= 0.5 * lineWidth.top;\n        }\n        if (lineWidth.bottom) {\n            y2 += 0.5 * lineWidth.bottom;\n        }\n        drawLine(lineWidth.left, x1, y1, x2, y2);\n    }\n    if (lineWidth.right) {\n        x1 = cursor.x + cell.width;\n        y1 = cursor.y;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.top) {\n            y1 -= 0.5 * lineWidth.top;\n        }\n        if (lineWidth.bottom) {\n            y2 += 0.5 * lineWidth.bottom;\n        }\n        drawLine(lineWidth.right, x1, y1, x2, y2);\n    }\n    function drawLine(width, x1, y1, x2, y2) {\n        doc.getDocument().setLineWidth(width);\n        doc.getDocument().line(x1, y1, x2, y2, 'S');\n    }\n}\nfunction getRemainingPageSpace(doc, table, isLastRow, cursor) {\n    var bottomContentHeight = table.settings.margin.bottom;\n    var showFoot = table.settings.showFoot;\n    if (showFoot === 'everyPage' || (showFoot === 'lastPage' && isLastRow)) {\n        bottomContentHeight += table.getFootHeight(table.columns);\n    }\n    return doc.pageSize().height - cursor.y - bottomContentHeight;\n}\nfunction addPage(doc, table, startPos, cursor, columns, suppressFooter) {\n    if (columns === void 0) { columns = []; }\n    if (suppressFooter === void 0) { suppressFooter = false; }\n    doc.applyStyles(doc.userStyles);\n    if (table.settings.showFoot === 'everyPage' && !suppressFooter) {\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n    // Add user content just before adding new page ensure it will\n    // be drawn above other things on the page\n    table.callEndPageHooks(doc, cursor);\n    var margin = table.settings.margin;\n    addTableBorder(doc, table, startPos, cursor);\n    nextPage(doc);\n    table.pageNumber++;\n    cursor.x = margin.left;\n    cursor.y = margin.top;\n    startPos.y = margin.top;\n    // call didAddPage hooks before any content is added to the page\n    table.callWillDrawPageHooks(doc, cursor);\n    if (table.settings.showHead === 'everyPage') {\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n        doc.applyStyles(doc.userStyles);\n    }\n}\nfunction nextPage(doc) {\n    var current = doc.pageNumber();\n    doc.setPage(current + 1);\n    var newCurrent = doc.pageNumber();\n    if (newCurrent === current) {\n        doc.addPage();\n        return true;\n    }\n    return false;\n}\n\nfunction applyPlugin(jsPDF) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    jsPDF.API.autoTable = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var options = args[0];\n        var input = parseInput(this, options);\n        var table = createTable(this, input);\n        drawTable(this, table);\n        return this;\n    };\n    // Assign false to enable `doc.lastAutoTable.finalY || 40` sugar\n    jsPDF.API.lastAutoTable = false;\n    jsPDF.API.autoTableText = function (text, x, y, styles) {\n        autoTableText(text, x, y, styles, this);\n    };\n    jsPDF.API.autoTableSetDefaults = function (defaults) {\n        DocHandler.setDefaults(defaults, this);\n        return this;\n    };\n    jsPDF.autoTableSetDefaults = function (defaults, doc) {\n        DocHandler.setDefaults(defaults, doc);\n    };\n    jsPDF.API.autoTableHtmlToJson = function (tableElem, includeHiddenElements) {\n        var _a;\n        if (includeHiddenElements === void 0) { includeHiddenElements = false; }\n        if (typeof window === 'undefined') {\n            console.error('Cannot run autoTableHtmlToJson in non browser environment');\n            return null;\n        }\n        var doc = new DocHandler(this);\n        var _b = parseHtml(doc, tableElem, window, includeHiddenElements, false), head = _b.head, body = _b.body;\n        var columns = ((_a = head[0]) === null || _a === void 0 ? void 0 : _a.map(function (c) { return c.content; })) || [];\n        return { columns: columns, rows: body, data: body };\n    };\n}\n\nvar _a;\nfunction autoTable(d, options) {\n    var input = parseInput(d, options);\n    var table = createTable(d, input);\n    drawTable(d, table);\n}\n// Experimental export\nfunction __createTable(d, options) {\n    var input = parseInput(d, options);\n    return createTable(d, input);\n}\nfunction __drawTable(d, table) {\n    drawTable(d, table);\n}\ntry {\n    if (typeof window !== 'undefined' && window) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        var anyWindow = window;\n        var jsPDF = anyWindow.jsPDF || ((_a = anyWindow.jspdf) === null || _a === void 0 ? void 0 : _a.jsPDF);\n        if (jsPDF) {\n            applyPlugin(jsPDF);\n        }\n    }\n}\ncatch (error) {\n    console.error('Could not apply autoTable plugin', error);\n}\n\nexport { Cell, CellHookData, Column, HookData, Row, Table, __createTable, __drawTable, applyPlugin, autoTable, autoTable as default };\n"], "mappings": ";;;AAIA,SAAS,cAAe,MAAM,GAAG,GAAG,QAAQ,KAAK;AAC7C,WAAS,UAAU,CAAC;AACpB,MAAI,uBAAuB;AAC3B,MAAI,IAAI,IAAI,SAAS;AACrB,MAAI,WAAW,IAAI,SAAS,YAAY,IAAI;AAC5C,MAAI,mBAAmB,IAAI,sBACrB,IAAI,oBAAoB,IACxB;AACN,MAAI,aAAa,WAAW;AAC5B,MAAI,aAAa;AACjB,MAAI,YAAY;AAChB,MAAI,YAAY;AAChB,MAAI,OAAO,WAAW,YAClB,OAAO,WAAW,YAClB,OAAO,WAAW,YAClB,OAAO,WAAW,SAAS;AAC3B,gBAAY,OAAO,SAAS,WAAW,KAAK,MAAM,UAAU,IAAI;AAChE,gBAAY,UAAU,UAAU;AAAA,EACpC;AAEA,OAAK,YAAY,IAAI;AACrB,MAAI,OAAO,WAAW;AAClB,SAAM,YAAY,IAAK;AAAA,WAClB,OAAO,WAAW;AACvB,SAAK,YAAY;AACrB,MAAI,OAAO,WAAW,YAAY,OAAO,WAAW,SAAS;AACzD,QAAI,YAAY;AAChB,QAAI,OAAO,WAAW;AAClB,mBAAa;AACjB,QAAI,aAAa,aAAa,GAAG;AAC7B,eAAS,QAAQ,GAAG,QAAQ,UAAU,QAAQ,SAAS;AACnD,YAAI,KAAK,UAAU,KAAK,GAAG,IAAI,IAAI,mBAAmB,UAAU,KAAK,CAAC,IAAI,WAAW,CAAC;AACtF,aAAK;AAAA,MACT;AACA,aAAO;AAAA,IACX;AACA,SAAK,IAAI,mBAAmB,IAAI,IAAI;AAAA,EACxC;AACA,MAAI,OAAO,WAAW,WAAW;AAC7B,QAAI,KAAK,MAAM,GAAG,GAAG,EAAE,UAAU,OAAO,YAAY,KAAK,OAAO,UAAU,CAAC;AAAA,EAC/E,OACK;AACD,QAAI,KAAK,MAAM,GAAG,CAAC;AAAA,EACvB;AACA,SAAO;AACX;AAEA,IAAI,iBAAiB,CAAC;AACtB,IAAI;AAAA;AAAA,EAA4B,WAAY;AACxC,aAASA,YAAW,eAAe;AAC/B,WAAK,gBAAgB;AACrB,WAAK,aAAa;AAAA;AAAA,QAEd,WAAW,cAAc,eACnB,KAAK,cAAc,aAAa,IAChC;AAAA,QACN,UAAU,cAAc,SAAS,YAAY;AAAA,QAC7C,WAAW,cAAc,SAAS,QAAQ,EAAE;AAAA,QAC5C,MAAM,cAAc,SAAS,QAAQ,EAAE;AAAA;AAAA,QAEvC,WAAW,cAAc,eACnB,KAAK,cAAc,aAAa,IAChC;AAAA;AAAA,QAEN,WAAW,cAAc,eACnB,KAAK,cAAc,aAAa,IAChC;AAAA,MACV;AAAA,IACJ;AACA,IAAAA,YAAW,cAAc,SAAU,UAAU,KAAK;AAC9C,UAAI,QAAQ,QAAQ;AAAE,cAAM;AAAA,MAAM;AAClC,UAAI,KAAK;AACL,YAAI,8BAA8B;AAAA,MACtC,OACK;AACD,yBAAiB;AAAA,MACrB;AAAA,IACJ;AACA,IAAAA,YAAW,aAAa,SAAU,GAAG;AACjC,UAAI,MAAM,QAAQ,CAAC,GAAG;AAClB,eAAO;AAAA,MACX,WACS,OAAO,MAAM,UAAU;AAC5B,eAAO,CAAC,GAAG,GAAG,CAAC;AAAA,MACnB,WACS,OAAO,MAAM,UAAU;AAC5B,eAAO,CAAC,CAAC;AAAA,MACb,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,cAAc,SAAU,QAAQ,UAAU;AAG3D,UAAIC,KAAI,IAAI;AACZ,UAAI,aAAa,QAAQ;AAAE,mBAAW;AAAA,MAAO;AAC7C,UAAI,OAAO,aAAa,KAAK,cAAc,cAAc;AACrD,aAAK,cAAc,aAAa,OAAO,SAAS;AAAA,MACpD;AACA,UAAI,KAAK,KAAK,cAAc,SAAS,QAAQ,GAAG,YAAY,GAAG,WAAW,WAAW,GAAG;AACxF,UAAI,OAAO;AACP,mBAAW,OAAO;AACtB,UAAI,OAAO,WAAW;AAClB,oBAAY,OAAO;AACnB,YAAI,sBAAsB,KAAK,YAAY,EAAE,QAAQ;AACrD,YAAI,uBACA,oBAAoB,QAAQ,SAAS,MAAM,MAC3C,KAAK,cAAc,cAAc;AAIjC,eAAK,cAAc,aAAa,oBAAoB,CAAC,CAAC;AACtD,sBAAY,oBAAoB,CAAC;AAAA,QACrC;AAAA,MACJ;AACA,WAAK,cAAc,QAAQ,UAAU,SAAS;AAC9C,UAAI,OAAO;AACP,aAAK,cAAc,YAAY,OAAO,QAAQ;AAClD,UAAI,UAAU;AACV;AAAA,MACJ;AACA,UAAI,QAAQD,YAAW,WAAW,OAAO,SAAS;AAClD,UAAI;AACA,SAACC,MAAK,KAAK,eAAe,aAAa,MAAMA,KAAI,KAAK;AAC1D,cAAQD,YAAW,WAAW,OAAO,SAAS;AAC9C,UAAI;AACA,SAAC,KAAK,KAAK,eAAe,aAAa,MAAM,IAAI,KAAK;AAC1D,cAAQA,YAAW,WAAW,OAAO,SAAS;AAC9C,UAAI;AACA,SAAC,KAAK,KAAK,eAAe,aAAa,MAAM,IAAI,KAAK;AAC1D,UAAI,OAAO,OAAO,cAAc,UAAU;AACtC,aAAK,cAAc,aAAa,OAAO,SAAS;AAAA,MACpD;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,kBAAkB,SAAU,MAAM,MAAM,MAAM;AAC/D,aAAO,KAAK,cAAc,gBAAgB,MAAM,MAAM,IAAI;AAAA,IAC9D;AASA,IAAAA,YAAW,UAAU,OAAO,SAAU,GAAG,GAAG,OAAO,QAAQ,WAAW;AAIlE,aAAO,KAAK,cAAc,KAAK,GAAG,GAAG,OAAO,QAAQ,SAAS;AAAA,IACjE;AACA,IAAAA,YAAW,UAAU,mBAAmB,WAAY;AAChD,aAAO,KAAK,cAAc,iBAAiB;AAAA,IAC/C;AACA,IAAAA,YAAW,UAAU,eAAe,SAAU,MAAM;AAChD,aAAO,KAAK,cAAc,aAAa,IAAI;AAAA,IAC/C;AACA,IAAAA,YAAW,UAAU,cAAc,WAAY;AAC3C,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,YAAW,UAAU,UAAU,SAAU,MAAM;AAC3C,WAAK,cAAc,QAAQ,IAAI;AAAA,IACnC;AACA,IAAAA,YAAW,UAAU,UAAU,WAAY;AACvC,aAAO,KAAK,cAAc,QAAQ;AAAA,IACtC;AACA,IAAAA,YAAW,UAAU,cAAc,WAAY;AAC3C,aAAO,KAAK,cAAc,YAAY;AAAA,IAC1C;AACA,IAAAA,YAAW,UAAU,mBAAmB,WAAY;AAChD,aAAO,kBAAkB,CAAC;AAAA,IAC9B;AACA,IAAAA,YAAW,UAAU,qBAAqB,WAAY;AAClD,aAAO,KAAK,cAAc,+BAA+B,CAAC;AAAA,IAC9D;AACA,IAAAA,YAAW,UAAU,WAAW,WAAY;AACxC,UAAI,WAAW,KAAK,cAAc,SAAS;AAE3C,UAAI,SAAS,SAAS,MAAM;AACxB,mBAAW,EAAE,OAAO,SAAS,SAAS,GAAG,QAAQ,SAAS,UAAU,EAAE;AAAA,MAC1E;AACA,aAAO;AAAA,IACX;AACA,IAAAA,YAAW,UAAU,cAAc,WAAY;AAC3C,aAAO,KAAK,cAAc,SAAS;AAAA,IACvC;AACA,IAAAA,YAAW,UAAU,sBAAsB,WAAY;AACnD,UAAI,MAAM,KAAK;AACf,aAAO,IAAI,sBAAsB,IAAI,oBAAoB,IAAI;AAAA,IACjE;AACA,IAAAA,YAAW,UAAU,gBAAgB,SAAU,UAAU;AACrD,aAAQ,WAAW,KAAK,YAAY,IAAK,KAAK,oBAAoB;AAAA,IACtE;AACA,IAAAA,YAAW,UAAU,aAAa,WAAY;AAC1C,UAAI,WAAW,KAAK,cAAc,SAAS,mBAAmB;AAC9D,UAAI,CAAC,UAAU;AAEX,eAAO,KAAK,cAAc,SAAS,iBAAiB;AAAA,MACxD;AACA,aAAO,SAAS;AAAA,IACpB;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAkBF,IAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,kBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUE,IAAGC,IAAG;AAAE,IAAAD,GAAE,YAAYC;AAAA,EAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,aAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,EAAG;AACpG,SAAO,cAAc,GAAG,CAAC;AAC7B;AAEA,SAAS,UAAU,GAAG,GAAG;AACrB,MAAI,OAAO,MAAM,cAAc,MAAM;AACjC,UAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;AAAA,EAAG;AACtC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACtF;AAOA,IAAI;AAAA;AAAA,EAA8B,SAAU,QAAQ;AAChD,cAAUC,eAAc,MAAM;AAC9B,aAASA,cAAa,SAAS;AAC3B,UAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,YAAM,WAAW;AACjB,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,KAAK;AAAA;AAEP,SAAS,cAAc,aAAa;AAChC,SAAO;AAAA,IACH,MAAM;AAAA;AAAA,IACN,WAAW;AAAA;AAAA,IACX,UAAU;AAAA;AAAA,IACV,WAAW;AAAA;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,UAAU;AAAA,IACV,aAAa,IAAI;AAAA;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,WAAW;AAAA;AAAA,IACX,eAAe;AAAA,IACf,cAAc;AAAA,EAClB;AACJ;AACA,SAAS,SAAS,MAAM;AACpB,MAAI,SAAS;AAAA,IACT,SAAS;AAAA,MACL,OAAO,EAAE,WAAW,KAAK,WAAW,IAAI,WAAW,SAAS;AAAA,MAC5D,MAAM,EAAE,WAAW,KAAK,WAAW,CAAC,IAAI,KAAK,GAAG,GAAG,WAAW,OAAO;AAAA,MACrE,MAAM,CAAC;AAAA,MACP,MAAM,EAAE,WAAW,KAAK,WAAW,CAAC,IAAI,KAAK,GAAG,GAAG,WAAW,OAAO;AAAA,MACrE,cAAc,EAAE,WAAW,IAAI;AAAA,IACnC;AAAA,IACA,MAAM;AAAA,MACF,OAAO;AAAA,QACH,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,MACf;AAAA,MACA,MAAM;AAAA,QACF,WAAW;AAAA,QACX,WAAW,CAAC,IAAI,KAAK,GAAG;AAAA,QACxB,WAAW;AAAA,QACX,WAAW;AAAA,MACf;AAAA,MACA,MAAM,CAAC;AAAA,MACP,MAAM;AAAA,QACF,WAAW;AAAA,QACX,WAAW,CAAC,IAAI,KAAK,GAAG;AAAA,QACxB,WAAW;AAAA,QACX,WAAW;AAAA,MACf;AAAA,MACA,cAAc,CAAC;AAAA,IACnB;AAAA,IACA,OAAO,EAAE,MAAM,EAAE,WAAW,OAAO,GAAG,MAAM,EAAE,WAAW,OAAO,EAAE;AAAA,EACtE;AACA,SAAO,OAAO,IAAI;AACtB;AAEA,SAAS,eAAe,MAAM,QAAQ,KAAK;AACvC,MAAI,YAAY,QAAQ,IAAI;AAC5B,MAAI,UAAU,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AAChD,MAAI,kBAAkB,QACjB,IAAI,SAAUC,OAAM;AAAE,WAAO,IAAI,aAAaA,KAAI;AAAA,EAAG,CAAC,EACtD,OAAO,SAAU,GAAG,GAAG;AAAE,WAAO,KAAK,IAAI,GAAG,CAAC;AAAA,EAAG,GAAG,CAAC;AACzD,SAAO;AACX;AACA,SAAS,eAAe,KAAK,OAAO,UAAU,QAAQ;AAClD,MAAI,YAAY,MAAM,SAAS;AAC/B,MAAI,YAAY,MAAM,SAAS;AAC/B,MAAI,YAAY,EAAE,WAAsB,UAAqB,CAAC;AAC9D,MAAI,YAAY,aAAa,WAAW,KAAK;AAC7C,MAAI,WAAW;AACX,QAAI,KAAK,SAAS,GAAG,SAAS,GAAG,MAAM,SAAS,IAAI,SAAS,EAAE,KAAK,GAAG,OAAO,IAAI,SAAS,GAAG,SAAS;AAAA,EAC3G;AACJ;AACA,SAAS,aAAa,WAAW,WAAW;AACxC,MAAI,WAAW,YAAY;AAC3B,MAAI,iBAAiB,aAAa,cAAc;AAChD,MAAI,YAAY,gBAAgB;AAC5B,WAAO;AAAA,EACX,WACS,UAAU;AACf,WAAO;AAAA,EACX,WACS,gBAAgB;AACrB,WAAO;AAAA,EACX,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,aAAa,OAAO,cAAc;AACvC,MAAIC,KAAI,IAAI,IAAI;AAChB,UAAQ,SAAS;AACjB,MAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,QAAI,MAAM,UAAU,GAAG;AACnB,aAAO;AAAA,QACH,KAAK,MAAM,CAAC;AAAA,QACZ,OAAO,MAAM,CAAC;AAAA,QACd,QAAQ,MAAM,CAAC;AAAA,QACf,MAAM,MAAM,CAAC;AAAA,MACjB;AAAA,IACJ,WACS,MAAM,WAAW,GAAG;AACzB,aAAO;AAAA,QACH,KAAK,MAAM,CAAC;AAAA,QACZ,OAAO,MAAM,CAAC;AAAA,QACd,QAAQ,MAAM,CAAC;AAAA,QACf,MAAM,MAAM,CAAC;AAAA,MACjB;AAAA,IACJ,WACS,MAAM,WAAW,GAAG;AACzB,aAAO;AAAA,QACH,KAAK,MAAM,CAAC;AAAA,QACZ,OAAO,MAAM,CAAC;AAAA,QACd,QAAQ,MAAM,CAAC;AAAA,QACf,MAAM,MAAM,CAAC;AAAA,MACjB;AAAA,IACJ,WACS,MAAM,WAAW,GAAG;AACzB,cAAQ,MAAM,CAAC;AAAA,IACnB,OACK;AACD,cAAQ;AAAA,IACZ;AAAA,EACJ;AACA,MAAI,OAAO,UAAU,UAAU;AAC3B,QAAI,OAAO,MAAM,aAAa,UAAU;AACpC,YAAM,MAAM,MAAM;AAClB,YAAM,SAAS,MAAM;AAAA,IACzB;AACA,QAAI,OAAO,MAAM,eAAe,UAAU;AACtC,YAAM,QAAQ,MAAM;AACpB,YAAM,OAAO,MAAM;AAAA,IACvB;AACA,WAAO;AAAA,MACH,OAAOA,MAAK,MAAM,UAAU,QAAQA,QAAO,SAASA,MAAK;AAAA,MACzD,MAAM,KAAK,MAAM,SAAS,QAAQ,OAAO,SAAS,KAAK;AAAA,MACvD,QAAQ,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,KAAK;AAAA,MAC3D,SAAS,KAAK,MAAM,YAAY,QAAQ,OAAO,SAAS,KAAK;AAAA,IACjE;AAAA,EACJ;AACA,MAAI,OAAO,UAAU,UAAU;AAC3B,YAAQ;AAAA,EACZ;AACA,SAAO,EAAE,KAAK,OAAO,OAAO,OAAO,QAAQ,OAAO,MAAM,MAAM;AAClE;AACA,SAAS,sBAAsB,KAAK,OAAO;AACvC,MAAI,UAAU,aAAa,MAAM,SAAS,QAAQ,CAAC;AACnD,SAAO,IAAI,SAAS,EAAE,SAAS,QAAQ,OAAO,QAAQ;AAC1D;AAKA,SAAS,SAAS,gBAAgB,SAAS,aAAa,OAAOC,SAAQ;AACnE,MAAI,SAAS,CAAC;AACd,MAAI,gBAAgB,KAAK;AACzB,MAAI,kBAAkB,WAAW,SAAS,SAAU,MAAM;AACtD,WAAOA,QAAO,iBAAiB,IAAI,EAAE,iBAAiB;AAAA,EAC1D,CAAC;AACD,MAAI,mBAAmB;AACnB,WAAO,YAAY;AACvB,MAAI,YAAY,WAAW,SAAS,SAAU,MAAM;AAChD,WAAOA,QAAO,iBAAiB,IAAI,EAAE,OAAO;AAAA,EAChD,CAAC;AACD,MAAI,aAAa;AACb,WAAO,YAAY;AACvB,MAAI,UAAU,aAAa,OAAO,WAAW;AAC7C,MAAI;AACA,WAAO,cAAc;AACzB,MAAI,kBAAkB;AACtB,MAAI,mBAAmB,gBAAgB;AACvC,MAAI,MAAM,MAAM;AAChB,MAAI,MAAM,sBAAsB,OAC5B,MAAM,qBAAqB,OAC3B,MAAM,oBAAoB,KAAK;AAC/B,QAAI,eAAe,WAAW,GAAG,KAAK,KAAK;AAC3C,QAAI;AACA,aAAO,YAAY;AAAA,EAC3B,OACK;AACD,WAAO,YAAY;AAAA,MACf,MAAM,WAAW,MAAM,cAAc,KAAK,KAAK;AAAA,MAC/C,QAAQ,WAAW,MAAM,gBAAgB,KAAK,KAAK;AAAA,MACnD,SAAS,WAAW,MAAM,iBAAiB,KAAK,KAAK;AAAA,MACrD,OAAO,WAAW,MAAM,eAAe,KAAK,KAAK;AAAA,IACrD;AAGA,QAAI,CAAC,OAAO,UAAU,KAAK;AACvB,UAAI,OAAO,UAAU,OAAO;AACxB,0BAAkB;AAAA,MACtB,WACS,OAAO,UAAU,QAAQ;AAC9B,0BAAkB;AAAA,MACtB,WACS,OAAO,UAAU,MAAM;AAC5B,0BAAkB;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,cAAc,WAAW,SAAS,SAAU,MAAM;AAClD,WAAOA,QAAO,iBAAiB,IAAI,EAAE,eAAe;AAAA,EACxD,CAAC;AACD,MAAI,eAAe;AACf,WAAO,YAAY;AACvB,MAAI,WAAW,CAAC,QAAQ,SAAS,UAAU,SAAS;AACpD,MAAI,SAAS,QAAQ,MAAM,SAAS,MAAM,IAAI;AAC1C,WAAO,SAAS,MAAM;AAAA,EAC1B;AACA,aAAW,CAAC,UAAU,UAAU,KAAK;AACrC,MAAI,SAAS,QAAQ,MAAM,aAAa,MAAM,IAAI;AAC9C,WAAO,SAAS,MAAM;AAAA,EAC1B;AACA,MAAI,MAAM,SAAS,MAAM,YAAY,EAAE;AACvC,MAAI,CAAC,MAAM,GAAG;AACV,WAAO,WAAW,MAAM;AAC5B,MAAI,YAAY,eAAe,KAAK;AACpC,MAAI;AACA,WAAO,YAAY;AACvB,MAAI,QAAQ,MAAM,cAAc,IAAI,YAAY;AAChD,MAAI,eAAe,QAAQ,IAAI,MAAM,IAAI;AACrC,WAAO,OAAO;AAAA,EAClB;AACA,SAAO;AACX;AACA,SAAS,eAAe,OAAO;AAC3B,MAAI,MAAM;AACV,MAAI,MAAM,eAAe,UACrB,MAAM,eAAe,YACrB,SAAS,MAAM,UAAU,KAAK,KAAK;AACnC,UAAM;AAAA,EACV;AACA,MAAI,MAAM,cAAc,YAAY,MAAM,cAAc,WAAW;AAC/D,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,WAAW,SAAS,aAAa;AACtC,MAAI,WAAW,UAAU,SAAS,WAAW;AAC7C,MAAI,CAAC;AACD,WAAO;AACX,MAAI,OAAO,SAAS,MAAM,wDAAwD;AAClF,MAAI,CAAC,QAAQ,CAAC,MAAM,QAAQ,IAAI,GAAG;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,QAAQ;AAAA,IACR,SAAS,KAAK,CAAC,CAAC;AAAA,IAChB,SAAS,KAAK,CAAC,CAAC;AAAA,IAChB,SAAS,KAAK,CAAC,CAAC;AAAA,EACpB;AACA,MAAI,QAAQ,SAAS,KAAK,CAAC,CAAC;AAC5B,MAAI,UAAU,KAAK,MAAM,MAAM,CAAC,CAAC,KAAK,MAAM,MAAM,CAAC,CAAC,KAAK,MAAM,MAAM,CAAC,CAAC,GAAG;AACtE,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,UAAU,MAAM,aAAa;AAClC,MAAI,KAAK,YAAY,IAAI;AACzB,MAAI,OAAO,sBACP,OAAO,iBACP,OAAO,aACP,OAAO,WAAW;AAClB,QAAI,KAAK,iBAAiB,MAAM;AAC5B,aAAO;AAAA,IACX;AACA,WAAO,UAAU,KAAK,eAAe,WAAW;AAAA,EACpD,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,aAAa,OAAO,aAAa;AACtC,MAAI,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACV;AACA,MAAI,gBAAgB,MAAM,KAAK;AAC/B,MAAI,eAAe,SAAS,MAAM,UAAU,IAAI,SAAS,MAAM,QAAQ,KAAK,cAAc;AAC1F,MAAI,eAAe,IAAI,IAAI,SAAU,GAAG;AACpC,WAAO,SAAS,KAAK,GAAG,IAAI;AAAA,EAChC,CAAC;AACD,MAAI,UAAU,aAAa,cAAc,CAAC;AAC1C,MAAI,cAAc,QAAQ,KAAK;AAC3B,YAAQ,MAAM;AAAA,EAClB;AACA,MAAI,cAAc,QAAQ,QAAQ;AAC9B,YAAQ,SAAS;AAAA,EACrB;AACA,SAAO;AACX;AAEA,SAAS,UAAU,KAAK,OAAOA,SAAQ,mBAAmB,QAAQ;AAC9D,MAAID,KAAI;AACR,MAAI,sBAAsB,QAAQ;AAAE,wBAAoB;AAAA,EAAO;AAC/D,MAAI,WAAW,QAAQ;AAAE,aAAS;AAAA,EAAO;AACzC,MAAI;AACJ,MAAI,OAAO,UAAU,UAAU;AAC3B,mBAAeC,QAAO,SAAS,cAAc,KAAK;AAAA,EACtD,OACK;AACD,mBAAe;AAAA,EACnB;AACA,MAAI,iBAAiB,OAAO,KAAK,IAAI,YAAY,CAAC;AAClD,MAAI,cAAc,IAAI,YAAY;AAClC,MAAI,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC;AAClC,MAAI,CAAC,cAAc;AACf,YAAQ,MAAM,8CAA8C,KAAK;AACjE,WAAO,EAAE,MAAY,MAAY,KAAW;AAAA,EAChD;AACA,WAAS,IAAI,GAAG,IAAI,aAAa,KAAK,QAAQ,KAAK;AAC/C,QAAI,UAAU,aAAa,KAAK,CAAC;AACjC,QAAI,WAAW,MAAMD,MAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,mBAAmB,QAAQA,QAAO,SAAS,SAASA,IAAG,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY;AACxM,QAAI,MAAM,gBAAgB,gBAAgB,aAAaC,SAAQ,SAAS,mBAAmB,MAAM;AACjG,QAAI,CAAC;AACD;AACJ,QAAI,YAAY,SAAS;AACrB,WAAK,KAAK,GAAG;AAAA,IACjB,WACS,YAAY,SAAS;AAC1B,WAAK,KAAK,GAAG;AAAA,IACjB,OACK;AAED,WAAK,KAAK,GAAG;AAAA,IACjB;AAAA,EACJ;AACA,SAAO,EAAE,MAAY,MAAY,KAAW;AAChD;AACA,SAAS,gBAAgB,gBAAgB,aAAaA,SAAQ,KAAK,eAAe,QAAQ;AACtF,MAAI,YAAY,IAAI,aAAa,GAAG;AACpC,WAAS,IAAI,GAAG,IAAI,IAAI,MAAM,QAAQ,KAAK;AACvC,QAAI,OAAO,IAAI,MAAM,CAAC;AACtB,QAAI,UAAUA,QAAO,iBAAiB,IAAI;AAC1C,QAAI,iBAAiB,QAAQ,YAAY,QAAQ;AAC7C,UAAIC,cAAa;AACjB,UAAI,QAAQ;AACR,QAAAA,cAAa,SAAS,gBAAgB,MAAM,aAAa,SAASD,OAAM;AAAA,MAC5E;AACA,gBAAU,KAAK;AAAA,QACX,SAAS,KAAK;AAAA,QACd,SAAS,KAAK;AAAA,QACd,QAAQC;AAAA,QACR,UAAU;AAAA,QACV,SAAS,iBAAiB,IAAI;AAAA,MAClC,CAAC;AAAA,IACL;AAAA,EACJ;AACA,MAAI,QAAQD,QAAO,iBAAiB,GAAG;AACvC,MAAI,UAAU,SAAS,MAAM,iBAAiB,MAAM,YAAY,SAAS;AACrE,WAAO;AAAA,EACX;AACJ;AACA,SAAS,iBAAiB,SAAS;AAE/B,MAAI,OAAO,QAAQ,UAAU,IAAI;AAGjC,OAAK,YAAY,KAAK,UAAU,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,GAAG;AAErE,OAAK,YAAY,KAAK,UACjB,MAAM,SAAS,EACf,IAAI,SAAU,MAAM;AAAE,WAAO,KAAK,KAAK;AAAA,EAAG,CAAC,EAC3C,KAAK,IAAI;AAEd,SAAO,KAAK,aAAa,KAAK,eAAe;AACjD;AAEA,SAAS,cAAc,QAAQ,UAAU,SAAS;AAC9C,WAAS,KAAK,GAAGD,MAAK,CAAC,QAAQ,UAAU,OAAO,GAAG,KAAKA,IAAG,QAAQ,MAAM;AACrE,QAAI,UAAUA,IAAG,EAAE;AACnB,QAAI,WAAW,OAAO,YAAY,UAAU;AACxC,cAAQ,MAAM,yDAAyD,OAAO,OAAO;AAAA,IACzF;AACA,QAAI,QAAQ,UAAU,OAAO,QAAQ,WAAW,UAAU;AACtD,cAAQ,MAAM,mCAAmC,QAAQ,MAAM;AAC/D,aAAO,QAAQ;AAAA,IACnB;AAAA,EACJ;AACJ;AAIA,SAAS,OAAO,QAAQ,GAAG,IAAI,IAAI,IAAI;AACnC,MAAI,UAAU,MAAM;AAChB,UAAM,IAAI,UAAU,4CAA4C;AAAA,EACpE;AACA,MAAI,KAAK,OAAO,MAAM;AACtB,WAAS,QAAQ,GAAG,QAAQ,UAAU,QAAQ,SAAS;AAEnD,QAAI,aAAa,UAAU,KAAK;AAChC,QAAI,cAAc,MAAM;AAEpB,eAAS,WAAW,YAAY;AAE5B,YAAI,OAAO,UAAU,eAAe,KAAK,YAAY,OAAO,GAAG;AAC3D,aAAG,OAAO,IAAI,WAAW,OAAO;AAAA,QACpC;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,WAAW,GAAG,SAAS;AAC5B,MAAI,MAAM,IAAI,WAAW,CAAC;AAC1B,MAAI,WAAW,IAAI,mBAAmB;AACtC,MAAI,SAAS,IAAI,iBAAiB;AAClC,gBAAc,QAAQ,UAAU,OAAO;AACvC,MAAI,UAAU,OAAO,CAAC,GAAG,QAAQ,UAAU,OAAO;AAClD,MAAI;AACJ,MAAI,OAAO,WAAW,aAAa;AAC/B,UAAM;AAAA,EACV;AACA,MAAI,SAAS,YAAY,QAAQ,UAAU,OAAO;AAClD,MAAI,QAAQ,WAAW,QAAQ,UAAU,OAAO;AAChD,MAAI,WAAW,cAAc,KAAK,OAAO;AACzC,MAAI,UAAU,eAAe,KAAK,SAAS,GAAG;AAC9C,SAAO,EAAE,IAAI,QAAQ,SAAS,SAAkB,OAAc,QAAgB,SAAmB;AACrG;AACA,SAAS,YAAY,QAAQ,QAAQ,QAAQ;AACzC,MAAI,eAAe;AAAA,IACf,QAAQ,CAAC;AAAA,IACT,YAAY,CAAC;AAAA,IACb,YAAY,CAAC;AAAA,IACb,YAAY,CAAC;AAAA,IACb,oBAAoB,CAAC;AAAA,IACrB,cAAc,CAAC;AAAA,EACnB;AACA,MAAI,UAAU,SAAUG,OAAM;AAC1B,QAAIA,UAAS,gBAAgB;AACzB,UAAI,WAAW,OAAOA,KAAI;AAC1B,UAAI,aAAa,OAAOA,KAAI;AAC5B,UAAI,UAAU,OAAOA,KAAI;AACzB,mBAAa,eAAe,OAAO,CAAC,GAAG,UAAU,YAAY,OAAO;AAAA,IACxE,OACK;AACD,UAAI,aAAa,CAAC,QAAQ,QAAQ,MAAM;AACxC,UAAI,SAAS,WAAW,IAAI,SAAU,MAAM;AAAE,eAAO,KAAKA,KAAI,KAAK,CAAC;AAAA,MAAG,CAAC;AACxE,mBAAaA,KAAI,IAAI,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,IACnE;AAAA,EACJ;AACA,WAAS,KAAK,GAAGH,MAAK,OAAO,KAAK,YAAY,GAAG,KAAKA,IAAG,QAAQ,MAAM;AACnE,QAAI,OAAOA,IAAG,EAAE;AAChB,YAAQ,IAAI;AAAA,EAChB;AACA,SAAO;AACX;AACA,SAAS,WAAW,QAAQ,UAAU,SAAS;AAC3C,MAAI,aAAa,CAAC,QAAQ,UAAU,OAAO;AAC3C,MAAI,SAAS;AAAA,IACT,cAAc,CAAC;AAAA,IACf,cAAc,CAAC;AAAA,IACf,aAAa,CAAC;AAAA,IACd,cAAc,CAAC;AAAA,IACf,aAAa,CAAC;AAAA,EAClB;AACA,WAAS,KAAK,GAAG,eAAe,YAAY,KAAK,aAAa,QAAQ,MAAM;AACxE,QAAI,UAAU,aAAa,EAAE;AAC7B,QAAI,QAAQ;AACR,aAAO,aAAa,KAAK,QAAQ,YAAY;AACjD,QAAI,QAAQ;AACR,aAAO,aAAa,KAAK,QAAQ,YAAY;AACjD,QAAI,QAAQ;AACR,aAAO,YAAY,KAAK,QAAQ,WAAW;AAC/C,QAAI,QAAQ;AACR,aAAO,aAAa,KAAK,QAAQ,YAAY;AACjD,QAAI,QAAQ;AACR,aAAO,YAAY,KAAK,QAAQ,WAAW;AAAA,EACnD;AACA,SAAO;AACX;AACA,SAAS,cAAc,KAAK,SAAS;AACjC,MAAIA,KAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAChD,MAAI,SAAS,aAAa,QAAQ,QAAQ,KAAK,IAAI,YAAY,CAAC;AAChE,MAAI,UAAUA,MAAK,UAAU,KAAK,QAAQ,MAAM,OAAO,QAAQA,QAAO,SAASA,MAAK,OAAO;AAC3F,MAAI;AACJ,MAAI,QAAQ,aAAa,MAAM;AAC3B,eAAW;AAAA,EACf,WACS,QAAQ,aAAa,OAAO;AACjC,eAAW;AAAA,EACf,OACK;AACD,gBAAY,KAAK,QAAQ,cAAc,QAAQ,OAAO,SAAS,KAAK;AAAA,EACxE;AACA,MAAI;AACJ,MAAI,QAAQ,aAAa,MAAM;AAC3B,eAAW;AAAA,EACf,WACS,QAAQ,aAAa,OAAO;AACjC,eAAW;AAAA,EACf,OACK;AACD,gBAAY,KAAK,QAAQ,cAAc,QAAQ,OAAO,SAAS,KAAK;AAAA,EACxE;AACA,MAAI,UAAU,KAAK,QAAQ,YAAY,QAAQ,OAAO,SAAS,KAAK;AACpE,MAAI,QAAQ,QAAQ,UAAU,SAAS,UAAU;AACjD,MAAI,sBAAsB,CAAC,CAAC,QAAQ;AACpC,MAAI,6BAA6B,KAAK,QAAQ,+BAA+B,QAAQ,OAAO,SAAS,KAAK;AAC1G,SAAO;AAAA,IACH,oBAAoB,KAAK,QAAQ,uBAAuB,QAAQ,OAAO,SAAS,KAAK;AAAA,IACrF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY,KAAK,QAAQ,eAAe,QAAQ,OAAO,SAAS,KAAK;AAAA,IACrE,eAAe,KAAK,QAAQ,kBAAkB,QAAQ,OAAO,SAAS,KAAK;AAAA,IAC3E,aAAa,KAAK,QAAQ,gBAAgB,QAAQ,OAAO,SAAS,KAAK;AAAA,IACvE;AAAA,IACA;AAAA,IACA,iBAAiB,KAAK,QAAQ,oBAAoB,QAAQ,OAAO,SAAS,KAAK;AAAA,IAC/E,iBAAiB,KAAK,QAAQ,oBAAoB,QAAQ,OAAO,SAAS,KAAK;AAAA,IAC/E;AAAA,IACA;AAAA,IACA,+BAA+B,KAAK,QAAQ,kCAAkC,QAAQ,OAAO,SAAS,KAAK;AAAA,EAC/G;AACJ;AACA,SAAS,UAAU,KAAK,YAAY;AAChC,MAAI,WAAW,IAAI,iBAAiB;AACpC,MAAI,KAAK,IAAI,YAAY;AACzB,MAAI,cAAc,IAAI,WAAW;AACjC,MAAI,4BAA4B;AAChC,MAAI,YAAY,SAAS,iBAAiB;AACtC,QAAI,aAAa,SAAS,kBAAkB,SAAS,aAAa;AAClE,gCAA4B,eAAe;AAAA,EAC/C;AACA,MAAI,OAAO,eAAe,UAAU;AAChC,WAAO;AAAA,EACX,WACS,cAAc,QAAQ,eAAe,OAAO;AACjD,QAAI,8BAA8B,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,WAAW,MAAM;AAG5G,aAAO,SAAS,SAAS,KAAK;AAAA,IAClC;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,eAAe,KAAK,SAASC,SAAQ;AAC1C,MAAI,OAAO,QAAQ,QAAQ,CAAC;AAC5B,MAAI,OAAO,QAAQ,QAAQ,CAAC;AAC5B,MAAI,OAAO,QAAQ,QAAQ,CAAC;AAC5B,MAAI,QAAQ,MAAM;AACd,QAAI,SAAS,QAAQ;AACrB,QAAIA,SAAQ;AACR,UAAI,cAAc,UAAU,KAAK,QAAQ,MAAMA,SAAQ,QAAQ,QAAQ,MAAM,KAAK,CAAC;AACnF,aAAO,YAAY,QAAQ;AAC3B,aAAO,YAAY,QAAQ;AAC3B,aAAO,YAAY,QAAQ;AAAA,IAC/B,OACK;AACD,cAAQ,MAAM,8CAA8C;AAAA,IAChE;AAAA,EACJ;AACA,MAAI,UAAU,QAAQ,WAAW,aAAa,MAAM,MAAM,IAAI;AAC9D,SAAO,EAAE,SAAkB,MAAY,MAAY,KAAW;AAClE;AACA,SAAS,aAAa,MAAM,MAAM,MAAM;AACpC,MAAI,WAAW,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC;AACjD,MAAI,SAAS,CAAC;AACd,SAAO,KAAK,QAAQ,EACf,OAAO,SAAU,KAAK;AAAE,WAAO,QAAQ;AAAA,EAAY,CAAC,EACpD,QAAQ,SAAU,KAAK;AACxB,QAAI,UAAU;AACd,QAAI;AACJ,QAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,cAAQ,SAAS,SAAS,GAAG,CAAC;AAAA,IAClC,OACK;AACD,cAAQ,SAAS,GAAG;AAAA,IACxB;AACA,QAAI,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK,GAAG;AACpD,iBAAW,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,YAAY;AAAA,IAC/E;AACA,aAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAC9B,UAAI,KAAK;AACT,UAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,aAAK,OAAO;AAAA,MAChB,OACK;AACD,aAAK,OAAO,IAAI,IAAI,IAAI,OAAO,CAAC,IAAI;AAAA,MACxC;AACA,UAAI,YAAY,EAAE,SAAS,GAAG;AAC9B,aAAO,KAAK,SAAS;AAAA,IACzB;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAEA,IAAI;AAAA;AAAA,EAA0B,2BAAY;AACtC,aAASG,UAAS,KAAK,OAAO,QAAQ;AAClC,WAAK,QAAQ;AACb,WAAK,aAAa,MAAM;AACxB,WAAK,WAAW,MAAM;AACtB,WAAK,SAAS;AACd,WAAK,MAAM,IAAI,YAAY;AAAA,IAC/B;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAI;AAAA;AAAA,EAA8B,SAAU,QAAQ;AAChD,cAAUC,eAAc,MAAM;AAC9B,aAASA,cAAa,KAAK,OAAO,MAAM,KAAK,QAAQ,QAAQ;AACzD,UAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,OAAO,MAAM,KAAK;AACrD,YAAM,OAAO;AACb,YAAM,MAAM;AACZ,YAAM,SAAS;AACf,YAAM,UAAU,IAAI;AACpB,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,QAAQ;AAAA;AAEV,IAAI;AAAA;AAAA,EAAuB,WAAY;AACnC,aAASC,OAAM,OAAO,SAAS;AAC3B,WAAK,aAAa;AAClB,WAAK,KAAK,MAAM;AAChB,WAAK,WAAW,MAAM;AACtB,WAAK,SAAS,MAAM;AACpB,WAAK,QAAQ,MAAM;AACnB,WAAK,UAAU,QAAQ;AACvB,WAAK,OAAO,QAAQ;AACpB,WAAK,OAAO,QAAQ;AACpB,WAAK,OAAO,QAAQ;AAAA,IACxB;AACA,IAAAA,OAAM,UAAU,gBAAgB,SAAU,SAAS;AAC/C,aAAO,KAAK,KAAK,OAAO,SAAU,KAAK,KAAK;AAAE,eAAO,MAAM,IAAI,iBAAiB,OAAO;AAAA,MAAG,GAAG,CAAC;AAAA,IAClG;AACA,IAAAA,OAAM,UAAU,gBAAgB,SAAU,SAAS;AAC/C,aAAO,KAAK,KAAK,OAAO,SAAU,KAAK,KAAK;AAAE,eAAO,MAAM,IAAI,iBAAiB,OAAO;AAAA,MAAG,GAAG,CAAC;AAAA,IAClG;AACA,IAAAA,OAAM,UAAU,UAAU,WAAY;AAClC,aAAO,KAAK,KAAK,OAAO,KAAK,IAAI,EAAE,OAAO,KAAK,IAAI;AAAA,IACvD;AACA,IAAAA,OAAM,UAAU,gBAAgB,SAAU,KAAK,UAAU,MAAM,KAAK,QAAQ,QAAQ;AAChF,eAAS,KAAK,GAAG,aAAa,UAAU,KAAK,WAAW,QAAQ,MAAM;AAClE,YAAI,UAAU,WAAW,EAAE;AAC3B,YAAI,OAAO,IAAI,aAAa,KAAK,MAAM,MAAM,KAAK,QAAQ,MAAM;AAChE,YAAI,SAAS,QAAQ,IAAI,MAAM;AAE/B,aAAK,OAAO,MAAM,QAAQ,KAAK,IAAI,IAAI,KAAK,OAAO,CAAC,KAAK,IAAI;AAC7D,YAAI,QAAQ;AACR,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,mBAAmB,SAAU,KAAK,QAAQ;AACtD,UAAI,YAAY,IAAI,UAAU;AAC9B,eAAS,KAAK,GAAGN,MAAK,KAAK,MAAM,aAAa,KAAKA,IAAG,QAAQ,MAAM;AAChE,YAAI,UAAUA,IAAG,EAAE;AACnB,gBAAQ,IAAI,SAAS,KAAK,MAAM,MAAM,CAAC;AAAA,MAC3C;AAAA,IACJ;AACA,IAAAM,OAAM,UAAU,wBAAwB,SAAU,KAAK,QAAQ;AAC3D,eAAS,KAAK,GAAGN,MAAK,KAAK,MAAM,cAAc,KAAKA,IAAG,QAAQ,MAAM;AACjE,YAAI,UAAUA,IAAG,EAAE;AACnB,gBAAQ,IAAI,SAAS,KAAK,MAAM,MAAM,CAAC;AAAA,MAC3C;AAAA,IACJ;AACA,IAAAM,OAAM,UAAU,WAAW,SAAU,WAAW;AAC5C,UAAI,OAAO,KAAK,SAAS,eAAe,UAAU;AAC9C,eAAO,KAAK,SAAS;AAAA,MACzB,WACS,KAAK,SAAS,eAAe,QAAQ;AAC1C,YAAI,eAAe,KAAK,QAAQ,OAAO,SAAU,OAAO,KAAK;AAAE,iBAAO,QAAQ,IAAI;AAAA,QAAc,GAAG,CAAC;AACpG,eAAO;AAAA,MACX,OACK;AACD,YAAI,SAAS,KAAK,SAAS;AAC3B,eAAO,YAAY,OAAO,OAAO,OAAO;AAAA,MAC5C;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAI;AAAA;AAAA,EAAqB,WAAY;AACjC,aAASC,KAAI,KAAK,OAAO,SAAS,OAAO,oBAAoB;AACzD,UAAI,uBAAuB,QAAQ;AAAE,6BAAqB;AAAA,MAAO;AACjE,WAAK,SAAS;AACd,WAAK,MAAM;AACX,UAAI,eAAe,cAAc;AAC7B,aAAK,MAAM,IAAI;AACf,aAAK,UAAU,IAAI;AAAA,MACvB;AACA,WAAK,QAAQ;AACb,WAAK,UAAU;AACf,WAAK,QAAQ;AACb,WAAK,qBAAqB;AAAA,IAC9B;AACA,IAAAA,KAAI,UAAU,mBAAmB,SAAU,SAAS;AAChD,UAAI,QAAQ;AACZ,aAAO,QAAQ,OAAO,SAAU,KAAK,QAAQ;AAAE,YAAIP;AAAI,eAAO,KAAK,IAAI,OAAOA,MAAK,MAAM,MAAM,OAAO,KAAK,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,WAAW,CAAC;AAAA,MAAG,GAAG,CAAC;AAAA,IAC7K;AACA,IAAAO,KAAI,UAAU,aAAa,SAAU,SAAS;AAC1C,UAAI,QAAQ;AACZ,aAAQ,QAAQ,OAAO,SAAU,QAAQ;AACrC,YAAI,OAAO,MAAM,MAAM,OAAO,KAAK;AACnC,YAAI,CAAC;AACD,iBAAO;AACX,eAAO,KAAK,UAAU;AAAA,MAC1B,CAAC,EAAE,SAAS;AAAA,IAChB;AACA,IAAAA,KAAI,UAAU,kBAAkB,SAAU,QAAQ,SAAS;AACvD,aAAO,KAAK,iBAAiB,OAAO,KAAK;AAAA,IAC7C;AACA,IAAAA,KAAI,UAAU,sBAAsB,SAAU,SAAS,KAAK;AACxD,UAAI,QAAQ;AACZ,aAAO,QAAQ,OAAO,SAAU,KAAK,QAAQ;AACzC,YAAI,OAAO,MAAM,MAAM,OAAO,KAAK;AACnC,YAAI,CAAC;AACD,iBAAO;AACX,YAAI,aAAa,IAAI,cAAc,KAAK,OAAO,QAAQ;AACvD,YAAI,WAAW,KAAK,QAAQ,UAAU;AACtC,YAAI,eAAe,WAAW;AAC9B,eAAO,eAAe,MAAM,eAAe;AAAA,MAC/C,GAAG,CAAC;AAAA,IACR;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAI;AAAA;AAAA,EAAsB,WAAY;AAClC,aAASC,MAAK,KAAK,QAAQ,SAAS;AAChC,UAAIR;AACJ,WAAK,gBAAgB;AACrB,WAAK,eAAe;AACpB,WAAK,eAAe;AACpB,WAAK,mBAAmB;AACxB,WAAK,WAAW;AAChB,WAAK,QAAQ;AACb,WAAK,SAAS;AACd,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,SAAS;AACd,WAAK,UAAU;AACf,WAAK,MAAM;AACX,UAAI,UAAU;AACd,UAAI,OAAO,QAAQ,OAAO,QAAQ,YAAY,CAAC,MAAM,QAAQ,GAAG,GAAG;AAC/D,aAAK,UAAU,IAAI,WAAW;AAC9B,aAAK,UAAU,IAAI,WAAW;AAC9B,mBAAWA,MAAK,IAAI,aAAa,QAAQA,QAAO,SAASA,MAAK;AAC9D,YAAI,IAAI,UAAU;AACd,eAAK,MAAM,IAAI;AAAA,QACnB;AAAA,MACJ,OACK;AACD,aAAK,UAAU;AACf,aAAK,UAAU;AAAA,MACnB;AAEA,UAAI,OAAO,WAAW,OAAO,KAAK,UAAU;AAC5C,UAAI,aAAa;AACjB,WAAK,OAAO,KAAK,MAAM,UAAU;AAAA,IACrC;AACA,IAAAQ,MAAK,UAAU,aAAa,WAAY;AACpC,UAAI;AACJ,UAAI,KAAK,OAAO,WAAW,OAAO;AAC9B,YAAI,KAAK,IAAI,KAAK,QAAQ,KAAK;AAAA,MACnC,WACS,KAAK,OAAO,WAAW,UAAU;AACtC,YAAI,KAAK,IAAI,KAAK,SAAS,KAAK,QAAQ,QAAQ;AAAA,MACpD,OACK;AACD,YAAI,YAAY,KAAK,SAAS,KAAK,QAAQ,UAAU;AACrD,YAAI,KAAK,IAAI,YAAY,IAAI,KAAK,QAAQ,KAAK;AAAA,MACnD;AACA,UAAI;AACJ,UAAI,KAAK,OAAO,WAAW,SAAS;AAChC,YAAI,KAAK,IAAI,KAAK,QAAQ,KAAK,QAAQ,OAAO;AAAA,MAClD,WACS,KAAK,OAAO,WAAW,UAAU;AACtC,YAAI,WAAW,KAAK,QAAQ,KAAK,QAAQ,YAAY;AACrD,YAAI,KAAK,IAAI,WAAW,IAAI,KAAK,QAAQ,MAAM;AAAA,MACnD,OACK;AACD,YAAI,KAAK,IAAI,KAAK,QAAQ,MAAM;AAAA,MACpC;AACA,aAAO,EAAE,GAAM,EAAK;AAAA,IACxB;AAEA,IAAAA,MAAK,UAAU,mBAAmB,SAAU,aAAa,kBAAkB;AACvE,UAAI,qBAAqB,QAAQ;AAAE,2BAAmB;AAAA,MAAM;AAC5D,UAAI,YAAY,MAAM,QAAQ,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;AAC9D,UAAI,aAAc,KAAK,OAAO,WAAW,cAAe;AACxD,UAAI,SAAS,YAAY,aAAa,KAAK,QAAQ,UAAU;AAC7D,aAAO,KAAK,IAAI,QAAQ,KAAK,OAAO,aAAa;AAAA,IACrD;AACA,IAAAA,MAAK,UAAU,UAAU,SAAU,MAAM;AACrC,UAAI,UAAU,aAAa,KAAK,OAAO,aAAa,CAAC;AACrD,UAAI,SAAS,YAAY;AACrB,eAAO,QAAQ,MAAM,QAAQ;AAAA,MACjC,WACS,SAAS,cAAc;AAC5B,eAAO,QAAQ,OAAO,QAAQ;AAAA,MAClC,OACK;AACD,eAAO,QAAQ,IAAI;AAAA,MACvB;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAI;AAAA;AAAA,EAAwB,WAAY;AACpC,aAASC,QAAO,SAAS,KAAK,OAAO;AACjC,WAAK,eAAe;AACpB,WAAK,mBAAmB;AACxB,WAAK,WAAW;AAChB,WAAK,QAAQ;AACb,WAAK,UAAU;AACf,WAAK,MAAM;AACX,WAAK,QAAQ;AAAA,IACjB;AACA,IAAAA,QAAO,UAAU,wBAAwB,SAAU,OAAO;AACtD,UAAI,MAAM;AACV,eAAS,KAAK,GAAGT,MAAK,MAAM,QAAQ,GAAG,KAAKA,IAAG,QAAQ,MAAM;AACzD,YAAI,MAAMA,IAAG,EAAE;AACf,YAAI,OAAO,IAAI,MAAM,KAAK,KAAK;AAC/B,YAAI,QAAQ,OAAO,KAAK,OAAO,cAAc,UAAU;AACnD,gBAAM,KAAK,IAAI,KAAK,KAAK,OAAO,SAAS;AAAA,QAC7C;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,WAAOS;AAAA,EACX,EAAE;AAAA;AAKF,SAAS,gBAAgB,KAAK,OAAO;AACjC,YAAU,KAAK,KAAK;AACpB,MAAI,mBAAmB,CAAC;AACxB,MAAI,oBAAoB;AACxB,QAAM,QAAQ,QAAQ,SAAU,QAAQ;AACpC,QAAI,cAAc,OAAO,sBAAsB,KAAK;AACpD,QAAI,aAAa;AAEb,aAAO,QAAQ;AAAA,IACnB,OACK;AAED,aAAO,QAAQ,OAAO;AACtB,uBAAiB,KAAK,MAAM;AAAA,IAChC;AACA,yBAAqB,OAAO;AAAA,EAChC,CAAC;AAED,MAAI,cAAc,MAAM,SAAS,IAAI,SAAS,EAAE,KAAK,IAAI;AAEzD,MAAI,aAAa;AACb,kBAAc,cAAc,kBAAkB,aAAa,SAAU,QAAQ;AACzE,aAAO,KAAK,IAAI,OAAO,kBAAkB,OAAO,QAAQ;AAAA,IAC5D,CAAC;AAAA,EACL;AAEA,MAAI,aAAa;AACb,kBAAc,cAAc,kBAAkB,aAAa,SAAU,QAAQ;AAAE,aAAO,OAAO;AAAA,IAAU,CAAC;AAAA,EAC5G;AACA,gBAAc,KAAK,IAAI,WAAW;AAClC,MAAI,CAAC,MAAM,SAAS,uBAChB,cAAc,MAAM,IAAI,YAAY,GAAG;AAKvC,kBAAc,cAAc,IAAI,cAAc,KAAK,MAAM,WAAW;AACpE,YAAQ,KAAK,yBAAyB,OAAO,aAAa,iCAAiC,CAAC;AAAA,EAChG;AACA,gBAAc,KAAK;AACnB,aAAW,OAAO,GAAG;AACrB,gBAAc,KAAK;AACvB;AACA,SAAS,UAAU,KAAK,OAAO;AAC3B,MAAI,KAAK,IAAI,YAAY;AACzB,MAAI,sBAAsB,MAAM,SAAS;AACzC,MAAI,qBAAqB,sBAAsB,KAAK,KAAK;AACzD,QAAM,QAAQ,EAAE,QAAQ,SAAU,KAAK;AACnC,aAAS,KAAK,GAAGT,MAAK,MAAM,SAAS,KAAKA,IAAG,QAAQ,MAAM;AACvD,UAAI,SAASA,IAAG,EAAE;AAClB,UAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,UAAI,CAAC;AACD;AACJ,UAAI,QAAQ,MAAM,MAAM;AACxB,YAAM,cAAc,KAAK,OAAO,MAAM,KAAK,QAAQ,IAAI;AACvD,UAAI,UAAU,KAAK,QAAQ,YAAY;AACvC,WAAK,eAAe,eAAe,KAAK,MAAM,KAAK,QAAQ,GAAG,IAAI;AAKlE,UAAI,mBAAmB,eAAe,KAAK,KAAK,KAAK,GAAG,EAAE,MAAM,cAAc,GAAG,KAAK,QAAQ,GAAG;AACjG,WAAK,mBAAmB,mBAAmB,KAAK,QAAQ,YAAY;AACpE,UAAI,OAAO,KAAK,OAAO,cAAc,UAAU;AAC3C,aAAK,WAAW,KAAK,OAAO;AAC5B,aAAK,eAAe,KAAK,OAAO;AAAA,MACpC,WACS,KAAK,OAAO,cAAc,UAC/B,wBAAwB,MAAM;AAE9B,YAAI,KAAK,eAAe,oBAAoB;AACxC,eAAK,WAAW;AAChB,eAAK,eAAe;AAAA,QACxB,OACK;AACD,eAAK,WAAW,KAAK;AACrB,eAAK,eAAe,KAAK;AAAA,QAC7B;AAAA,MACJ,OACK;AAED,YAAI,kBAAkB,KAAK;AAC3B,aAAK,WAAW,KAAK,OAAO,gBAAgB;AAC5C,aAAK,eAAe,KAAK;AACzB,YAAI,KAAK,WAAW,KAAK,cAAc;AACnC,eAAK,eAAe,KAAK;AAAA,QAC7B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,QAAM,QAAQ,EAAE,QAAQ,SAAU,KAAK;AACnC,aAAS,KAAK,GAAGA,MAAK,MAAM,SAAS,KAAKA,IAAG,QAAQ,MAAM;AACvD,UAAI,SAASA,IAAG,EAAE;AAClB,UAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AAGjC,UAAI,QAAQ,KAAK,YAAY,GAAG;AAC5B,eAAO,eAAe,KAAK,IAAI,OAAO,cAAc,KAAK,YAAY;AACrE,eAAO,WAAW,KAAK,IAAI,OAAO,UAAU,KAAK,QAAQ;AACzD,eAAO,mBAAmB,KAAK,IAAI,OAAO,kBAAkB,KAAK,gBAAgB;AAAA,MACrF,OACK;AAOD,YAAI,eAAe,MAAM,OAAO,aAAa,OAAO,OAAO,KACvD,MAAM,OAAO,aAAa,OAAO,KAAK,KACtC,CAAC;AACL,YAAI,YAAY,aAAa,aAAa,aAAa;AACvD,YAAI,aAAa,OAAO,cAAc,UAAU;AAC5C,iBAAO,WAAW;AAClB,iBAAO,eAAe;AAAA,QAC1B;AAAA,MACJ;AACA,UAAI,MAAM;AAEN,YAAI,KAAK,UAAU,KAAK,CAAC,OAAO,UAAU;AACtC,iBAAO,WAAW,KAAK;AAAA,QAC3B;AACA,YAAI,KAAK,UAAU,KAAK,CAAC,OAAO,cAAc;AAC1C,iBAAO,eAAe,KAAK;AAAA,QAC/B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAIA,SAAS,cAAc,SAAS,aAAa,aAAa;AACtD,MAAI,qBAAqB;AACzB,MAAI,kBAAkB,QAAQ,OAAO,SAAU,KAAKU,SAAQ;AAAE,WAAO,MAAMA,QAAO;AAAA,EAAc,GAAG,CAAC;AACpG,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,QAAI,SAAS,QAAQ,CAAC;AACtB,QAAI,QAAQ,OAAO,eAAe;AAClC,QAAI,kBAAkB,qBAAqB;AAC3C,QAAI,iBAAiB,OAAO,QAAQ;AACpC,QAAI,WAAW,YAAY,MAAM;AACjC,QAAI,WAAW,iBAAiB,WAAW,WAAW;AACtD,mBAAe,WAAW,OAAO;AACjC,WAAO,QAAQ;AAAA,EACnB;AACA,gBAAc,KAAK,MAAM,cAAc,IAAI,IAAI;AAG/C,MAAI,aAAa;AACb,QAAI,mBAAmB,QAAQ,OAAO,SAAUA,SAAQ;AACpD,aAAO,cAAc,IACfA,QAAO,QAAQ,YAAYA,OAAM,IACjC;AAAA,IACV,CAAC;AACD,QAAI,iBAAiB,QAAQ;AACzB,oBAAc,cAAc,kBAAkB,aAAa,WAAW;AAAA,IAC1E;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,cAAc,OAAO;AAC1B,MAAI,eAAe,CAAC;AACpB,MAAI,kBAAkB;AACtB,MAAI,MAAM,MAAM,QAAQ;AACxB,WAAS,WAAW,GAAG,WAAW,IAAI,QAAQ,YAAY;AACtD,QAAI,MAAM,IAAI,QAAQ;AACtB,aAAS,KAAK,GAAGV,MAAK,MAAM,SAAS,KAAKA,IAAG,QAAQ,MAAM;AACvD,UAAI,SAASA,IAAG,EAAE;AAClB,UAAI,OAAO,aAAa,OAAO,KAAK;AACpC,UAAI,kBAAkB,GAAG;AACrB;AACA,eAAO,IAAI,MAAM,OAAO,KAAK;AAAA,MACjC,WACS,MAAM;AACX,aAAK,KAAK,UAAU,IAAI;AACxB,0BAAkB,KAAK,KAAK;AAC5B,eAAO,IAAI,MAAM,OAAO,KAAK;AAC7B,aAAK;AACL,YAAI,KAAK,QAAQ,GAAG;AAChB,iBAAO,aAAa,OAAO,KAAK;AAAA,QACpC;AAAA,MACJ,OACK;AACD,YAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,YAAI,CAAC,MAAM;AACP;AAAA,QACJ;AACA,aAAK,SAAS,IAAI;AAClB,YAAI,KAAK,UAAU,GAAG;AAClB,cAAI,YAAY,IAAI,SAAS;AAC7B,cAAI,OAAO,KAAK,UAAU,YAAY,YAAY,KAAK;AACvD,uBAAa,OAAO,KAAK,IAAI,EAAE,MAAY,MAAY,IAAS;AAAA,QACpE;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,cAAc,OAAO;AAC1B,MAAI,MAAM,MAAM,QAAQ;AACxB,WAAS,WAAW,GAAG,WAAW,IAAI,QAAQ,YAAY;AACtD,QAAI,MAAM,IAAI,QAAQ;AACtB,QAAI,cAAc;AAClB,QAAI,uBAAuB;AAC3B,QAAI,eAAe;AACnB,aAAS,cAAc,GAAG,cAAc,MAAM,QAAQ,QAAQ,eAAe;AACzE,UAAI,SAAS,MAAM,QAAQ,WAAW;AAEtC,sBAAgB;AAChB,UAAI,eAAe,KAAK,MAAM,QAAQ,cAAc,CAAC,GAAG;AACpD,gCAAwB,OAAO;AAC/B,eAAO,IAAI,MAAM,OAAO,KAAK;AAAA,MACjC,WACS,aAAa;AAClB,YAAI,OAAO;AACX,eAAO,IAAI,MAAM,OAAO,KAAK;AAC7B,sBAAc;AACd,aAAK,QAAQ,OAAO,QAAQ;AAAA,MAChC,OACK;AACD,YAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,YAAI,CAAC;AACD;AACJ,uBAAe,KAAK;AACpB,+BAAuB;AACvB,YAAI,KAAK,UAAU,GAAG;AAClB,wBAAc;AACd,kCAAwB,OAAO;AAC/B;AAAA,QACJ;AACA,aAAK,QAAQ,OAAO,QAAQ;AAAA,MAChC;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,WAAW,OAAO,KAAK;AAC5B,MAAI,gBAAgB,EAAE,OAAO,GAAG,QAAQ,EAAE;AAC1C,WAAS,KAAK,GAAGA,MAAK,MAAM,QAAQ,GAAG,KAAKA,IAAG,QAAQ,MAAM;AACzD,QAAI,MAAMA,IAAG,EAAE;AACf,aAAS,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK,GAAG,QAAQ,MAAM;AACvD,UAAI,SAAS,GAAG,EAAE;AAClB,UAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,UAAI,CAAC;AACD;AACJ,UAAI,YAAY,KAAK,QAAQ,IAAI;AACjC,UAAI,YAAY,KAAK,QAAQ,KAAK,QAAQ,YAAY;AACtD,UAAI,KAAK,OAAO,aAAa,aAAa;AAEtC,aAAK,OAAO,IAAI,gBAAgB,KAAK,MAAM,YAAY,IAAI,IAAI,YAAY,GAAG,EAAE,UAAU,KAAK,OAAO,SAAS,CAAC;AAAA,MACpH,WACS,KAAK,OAAO,aAAa,aAAa;AAC3C,aAAK,OAAO,UAAU,KAAK,MAAM,WAAW,KAAK,QAAQ,KAAK,KAAK;AAAA,MACvE,WACS,KAAK,OAAO,aAAa,UAAU;AACxC,aAAK,OAAO,UAAU,KAAK,MAAM,WAAW,KAAK,QAAQ,KAAK,EAAE;AAAA,MACpE,WACS,OAAO,KAAK,OAAO,aAAa,YAAY;AACjD,YAAI,SAAS,KAAK,OAAO,SAAS,KAAK,MAAM,SAAS;AACtD,YAAI,OAAO,WAAW,UAAU;AAC5B,eAAK,OAAO,CAAC,MAAM;AAAA,QACvB,OACK;AACD,eAAK,OAAO;AAAA,QAChB;AAAA,MACJ;AACA,WAAK,gBAAgB,KAAK,iBAAiB,IAAI,YAAY,GAAG,IAAI,oBAAoB,CAAC;AACvF,UAAI,oBAAoB,KAAK,gBAAgB,KAAK;AAClD,UAAI,KAAK,UAAU,KACf,cAAc,QAAQ,cAAc,SAChC,oBAAoB,KAAK,SAAS;AACtC,wBAAgB,EAAE,QAAQ,mBAAmB,OAAO,KAAK,QAAQ;AAAA,MACrE,WACS,iBAAiB,cAAc,QAAQ,GAAG;AAC/C,YAAI,cAAc,SAAS,mBAAmB;AAC1C,8BAAoB,cAAc;AAAA,QACtC;AAAA,MACJ;AACA,UAAI,oBAAoB,IAAI,QAAQ;AAChC,YAAI,SAAS;AAAA,MACjB;AAAA,IACJ;AACA,kBAAc;AAAA,EAClB;AACJ;AACA,SAAS,UAAU,MAAM,OAAO,QAAQ,KAAK,UAAU;AACnD,SAAO,KAAK,IAAI,SAAU,KAAK;AAAE,WAAO,aAAa,KAAK,OAAO,QAAQ,KAAK,QAAQ;AAAA,EAAG,CAAC;AAC9F;AACA,SAAS,aAAa,MAAM,OAAO,QAAQ,KAAK,UAAU;AACtD,MAAI,YAAY,MAAQ,IAAI,YAAY;AACxC,UAAQ,KAAK,KAAK,QAAQ,SAAS,IAAI;AACvC,MAAI,SAAS,eAAe,MAAM,QAAQ,GAAG,GAAG;AAC5C,WAAO;AAAA,EACX;AACA,SAAO,QAAQ,eAAe,OAAO,UAAU,QAAQ,GAAG,GAAG;AACzD,QAAI,KAAK,UAAU,GAAG;AAClB;AAAA,IACJ;AACA,WAAO,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;AAAA,EAC5C;AACA,SAAO,KAAK,KAAK,IAAI;AACzB;AAEA,SAAS,YAAY,UAAU,OAAO;AAClC,MAAI,MAAM,IAAI,WAAW,QAAQ;AACjC,MAAI,UAAU,aAAa,OAAO,IAAI,YAAY,CAAC;AACnD,MAAI,QAAQ,IAAI,MAAM,OAAO,OAAO;AACpC,kBAAgB,KAAK,KAAK;AAC1B,MAAI,YAAY,IAAI,UAAU;AAC9B,SAAO;AACX;AACA,SAAS,aAAa,OAAO,IAAI;AAC7B,MAAI,UAAU,MAAM;AACpB,MAAI,UAAU,cAAc,QAAQ,OAAO;AAE3C,MAAI,QAAQ,KAAK,WAAW,GAAG;AAC3B,QAAI,aAAa,mBAAmB,SAAS,MAAM;AACnD,QAAI;AACA,cAAQ,KAAK,KAAK,UAAU;AAAA,EACpC;AACA,MAAI,QAAQ,KAAK,WAAW,GAAG;AAC3B,QAAI,aAAa,mBAAmB,SAAS,MAAM;AACnD,QAAI;AACA,cAAQ,KAAK,KAAK,UAAU;AAAA,EACpC;AACA,MAAI,QAAQ,MAAM,SAAS;AAC3B,MAAI,SAAS,MAAM;AACnB,SAAO;AAAA,IACH;AAAA,IACA,MAAM,aAAa,QAAQ,QAAQ,MAAM,SAAS,QAAQ,OAAO,EAAE;AAAA,IACnE,MAAM,aAAa,QAAQ,QAAQ,MAAM,SAAS,QAAQ,OAAO,EAAE;AAAA,IACnE,MAAM,aAAa,QAAQ,QAAQ,MAAM,SAAS,QAAQ,OAAO,EAAE;AAAA,EACvE;AACJ;AACA,SAAS,aAAa,aAAa,aAAa,SAAS,YAAY,OAAO,aAAa;AACrF,MAAI,wBAAwB,CAAC;AAC7B,MAAI,SAAS,YAAY,IAAI,SAAU,QAAQ,UAAU;AACrD,QAAI,wBAAwB;AAC5B,QAAI,QAAQ,CAAC;AACb,QAAI,gBAAgB;AACpB,QAAI,kBAAkB;AACtB,aAAS,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AAC/D,UAAI,SAAS,UAAU,EAAE;AACzB,UAAI,sBAAsB,OAAO,KAAK,KAAK,QACvC,sBAAsB,OAAO,KAAK,EAAE,SAAS,GAAG;AAChD,YAAI,oBAAoB,GAAG;AACvB,cAAI,UAAU;AACd,cAAI,MAAM,QAAQ,MAAM,GAAG;AACvB,sBACI,OAAO,OAAO,QAAQ,gBAAgB,qBAAqB;AAAA,UACnE,OACK;AACD,sBAAU,OAAO,OAAO,OAAO;AAAA,UACnC;AACA,cAAI,kBAAkB,CAAC;AACvB,cAAI,OAAO,YAAY,YAAY,CAAC,MAAM,QAAQ,OAAO,GAAG;AACxD,+BAAmB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WAAW,CAAC;AAAA,UAC7F;AACA,cAAI,SAAS,WAAW,aAAa,QAAQ,UAAU,OAAO,YAAY,aAAa,eAAe;AACtG,cAAI,OAAO,IAAI,KAAK,SAAS,QAAQ,WAAW;AAGhD,gBAAM,OAAO,OAAO,IAAI;AACxB,gBAAM,OAAO,KAAK,IAAI;AACtB,4BAAkB,KAAK,UAAU;AACjC,gCAAsB,OAAO,KAAK,IAAI;AAAA,YAClC,MAAM,KAAK,UAAU;AAAA,YACrB,OAAO;AAAA,UACX;AAAA,QACJ,OACK;AACD;AACA;AAAA,QACJ;AAAA,MACJ,OACK;AACD,8BAAsB,OAAO,KAAK,EAAE;AACpC,0BAAkB,sBAAsB,OAAO,KAAK,EAAE;AACtD;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,IAAI,IAAI,QAAQ,UAAU,aAAa,KAAK;AAAA,EACvD,CAAC;AACD,SAAO;AACX;AACA,SAAS,mBAAmB,SAAS,SAAS;AAC1C,MAAI,aAAa,CAAC;AAClB,UAAQ,QAAQ,SAAU,KAAK;AAC3B,QAAI,IAAI,OAAO,MAAM;AACjB,UAAI,QAAQ,gBAAgB,SAAS,IAAI,GAAG;AAC5C,UAAI,SAAS;AACT,mBAAW,IAAI,OAAO,IAAI;AAAA,IAClC;AAAA,EACJ,CAAC;AACD,SAAO,OAAO,KAAK,UAAU,EAAE,SAAS,IAAI,aAAa;AAC7D;AACA,SAAS,gBAAgB,SAAS,QAAQ;AACtC,MAAI,YAAY,QAAQ;AACpB,QAAI,OAAO,WAAW,UAAU;AAC5B,aAAO,OAAO,UAAU;AAAA,IAC5B,WACS,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;AAC/D,aAAO;AAAA,IACX;AAAA,EACJ,WACS,YAAY,UAAU,OAAO,WAAW,UAAU;AACvD,WAAO,OAAO;AAAA,EAClB;AACA,SAAO;AACX;AACA,SAAS,cAAc,SAAS;AAC5B,SAAO,QAAQ,IAAI,SAAU,OAAO,OAAO;AACvC,QAAIA;AACJ,QAAI;AACJ,QAAI,OAAO,UAAU,UAAU;AAC3B,aAAOA,MAAK,MAAM,aAAa,QAAQA,QAAO,SAASA,MAAK;AAAA,IAChE,OACK;AACD,YAAM;AAAA,IACV;AACA,WAAO,IAAI,OAAO,KAAK,OAAO,KAAK;AAAA,EACvC,CAAC;AACL;AACA,SAAS,WAAW,aAAa,QAAQ,UAAU,WAAW,QAAQ,aAAa,iBAAiB;AAChG,MAAI,QAAQ,SAAS,SAAS;AAC9B,MAAI;AACJ,MAAI,gBAAgB,QAAQ;AACxB,oBAAgB,OAAO;AAAA,EAC3B,WACS,gBAAgB,QAAQ;AAC7B,oBAAgB,OAAO;AAAA,EAC3B,WACS,gBAAgB,QAAQ;AAC7B,oBAAgB,OAAO;AAAA,EAC3B;AACA,MAAI,cAAc,OAAO,CAAC,GAAG,MAAM,OAAO,MAAM,WAAW,GAAG,OAAO,QAAQ,aAAa;AAC1F,MAAI,eAAe,OAAO,aAAa,OAAO,OAAO,KACjD,OAAO,aAAa,OAAO,KAAK,KAChC,CAAC;AACL,MAAI,YAAY,gBAAgB,SAAS,eAAe,CAAC;AACzD,MAAI,YAAY,gBAAgB,UAAU,WAAW,MAAM,IACrD,OAAO,CAAC,GAAG,MAAM,cAAc,OAAO,kBAAkB,IACxD,CAAC;AACP,MAAI,eAAe,cAAc,WAAW;AAC5C,MAAI,cAAc,OAAO,CAAC,GAAG,cAAc,aAAa,WAAW,SAAS;AAC5E,SAAO,OAAO,aAAa,eAAe;AAC9C;AAGA,SAAS,uBAAuB,KAAK,OAAO,QAAQ;AAChD,MAAIA;AACJ,MAAI,WAAW,QAAQ;AAAE,aAAS,CAAC;AAAA,EAAG;AAEtC,MAAI,iBAAiB,sBAAsB,KAAK,KAAK;AAErD,MAAI,mBAAmB,oBAAI,IAAI;AAC/B,MAAI,aAAa,CAAC;AAClB,MAAI,UAAU,CAAC;AACf,MAAI,4BAA4B,CAAC;AACjC,MAAI,MAAM,QAAQ,MAAM,SAAS,yBAAyB,GAAG;AACzD,gCAA4B,MAAM,SAAS;AAAA,EAE/C,WACS,OAAO,MAAM,SAAS,8BAA8B,YACzD,OAAO,MAAM,SAAS,8BAA8B,UAAU;AAC9D,gCAA4B,CAAC,MAAM,SAAS,yBAAyB;AAAA,EACzE;AAEA,4BAA0B,QAAQ,SAAU,OAAO;AAC/C,QAAI,MAAM,MAAM,QAAQ,KAAK,SAAU,MAAM;AAAE,aAAO,KAAK,YAAY,SAAS,KAAK,UAAU;AAAA,IAAO,CAAC;AACvG,QAAI,OAAO,CAAC,iBAAiB,IAAI,IAAI,KAAK,GAAG;AACzC,uBAAiB,IAAI,IAAI,OAAO,IAAI;AACpC,iBAAW,KAAK,IAAI,KAAK;AACzB,cAAQ,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC;AACrC,wBAAkB,IAAI;AAAA,IAC1B;AAAA,EACJ,CAAC;AACD,MAAI,QAAQ;AACZ,MAAI,KAAKA,MAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,WAAW,QAAQA,QAAO,SAASA,MAAK;AAC7G,SAAO,IAAI,MAAM,QAAQ,QAAQ;AAE7B,QAAI,iBAAiB,IAAI,CAAC,GAAG;AACzB;AACA;AAAA,IACJ;AACA,QAAI,WAAW,MAAM,QAAQ,CAAC,EAAE;AAEhC,QAAI,SAAS,kBAAkB,UAAU;AACrC,cAAQ;AACR,iBAAW,KAAK,CAAC;AACjB,cAAQ,KAAK,MAAM,QAAQ,CAAC,CAAC;AAC7B,wBAAkB;AAAA,IACtB,OACK;AACD;AAAA,IACJ;AACA;AAAA,EACJ;AACA,SAAO,EAAE,YAAwB,SAAkB,WAAW,IAAI,EAAE;AACxE;AACA,SAAS,gCAAgC,KAAK,OAAO;AACjD,MAAI,aAAa,CAAC;AAClB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,QAAQ,KAAK;AAC3C,QAAI,SAAS,uBAAuB,KAAK,OAAO,EAAE,OAAO,EAAE,CAAC;AAC5D,QAAI,OAAO,QAAQ,QAAQ;AACvB,iBAAW,KAAK,MAAM;AACtB,UAAI,OAAO;AAAA,IACf;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,UAAU,UAAU,OAAO;AAChC,MAAI,WAAW,MAAM;AACrB,MAAI,SAAS,SAAS;AACtB,MAAI,SAAS,SAAS;AACtB,MAAI,SAAS,EAAE,GAAG,OAAO,MAAM,GAAG,OAAO;AACzC,MAAI,iBAAiB,MAAM,cAAc,MAAM,OAAO,IAAI,MAAM,cAAc,MAAM,OAAO;AAC3F,MAAI,oBAAoB,SAAS,OAAO,SAAS;AACjD,MAAI,SAAS,cAAc,SAAS;AAChC,QAAI,OAAO,MAAM;AACjB,QAAI,cAAc,KAAK,OAAO,SAAU,KAAK,KAAK;AAAE,aAAO,MAAM,IAAI;AAAA,IAAQ,GAAG,CAAC;AACjF,yBAAqB;AAAA,EACzB;AACA,MAAI,MAAM,IAAI,WAAW,QAAQ;AACjC,MAAI,SAAS,cAAc,YACtB,SAAS,UAAU,QAAQ,oBAAoB,IAAI,SAAS,EAAE,QAAS;AACxE,aAAS,GAAG;AACZ,WAAO,IAAI,OAAO;AAAA,EACtB;AACA,QAAM,sBAAsB,KAAK,MAAM;AACvC,MAAI,WAAW,OAAO,CAAC,GAAG,MAAM;AAChC,QAAM,kBAAkB,IAAI,WAAW;AACvC,MAAI,SAAS,qBAAqB;AAE9B,sCAAkC,KAAK,OAAO,UAAU,MAAM;AAAA,EAClE,OACK;AAED,QAAI,YAAY,IAAI,UAAU;AAC9B,QAAI,SAAS,aAAa,eACtB,SAAS,aAAa,aAAa;AACnC,YAAM,KAAK,QAAQ,SAAU,KAAK;AAC9B,eAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,MAAM,OAAO;AAAA,MAC1D,CAAC;AAAA,IACL;AACA,QAAI,YAAY,IAAI,UAAU;AAC9B,UAAM,KAAK,QAAQ,SAAU,KAAK,OAAO;AACrC,UAAI,YAAY,UAAU,MAAM,KAAK,SAAS;AAC9C,mBAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ,MAAM,OAAO;AAAA,IAC5E,CAAC;AACD,QAAI,YAAY,IAAI,UAAU;AAC9B,QAAI,SAAS,aAAa,cAAc,SAAS,aAAa,aAAa;AACvE,YAAM,KAAK,QAAQ,SAAU,KAAK;AAC9B,eAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,MAAM,OAAO;AAAA,MAC1D,CAAC;AAAA,IACL;AAAA,EACJ;AACA,iBAAe,KAAK,OAAO,UAAU,MAAM;AAC3C,QAAM,iBAAiB,KAAK,MAAM;AAClC,QAAM,SAAS,OAAO;AACtB,WAAS,gBAAgB;AACzB,MAAI,YAAY,IAAI,UAAU;AAClC;AACA,SAAS,kCAAkC,KAAK,OAAO,UAAU,QAAQ;AAErE,MAAI,yBAAyB,gCAAgC,KAAK,KAAK;AACvE,MAAI,WAAW,MAAM;AACrB,MAAI,SAAS,iCAAiC,gBAAgB;AAC1D,2BAAuB,QAAQ,SAAU,gBAAgB,OAAO;AAC5D,UAAI,YAAY,IAAI,UAAU;AAE9B,UAAI,QAAQ,GAAG;AAGX,gBAAQ,KAAK,OAAO,UAAU,QAAQ,eAAe,SAAS,IAAI;AAAA,MACtE,OACK;AAED,kBAAU,KAAK,OAAO,QAAQ,eAAe,OAAO;AAAA,MACxD;AAEA,gBAAU,KAAK,OAAO,UAAU,QAAQ,eAAe,OAAO;AAC9D,gBAAU,KAAK,OAAO,QAAQ,eAAe,OAAO;AAAA,IACxD,CAAC;AAAA,EACL,OACK;AACD,QAAI,2BAA2B;AAC/B,QAAI,0BAA0B,uBAAuB,CAAC;AACtD,QAAI,UAAU,WAAY;AAEtB,UAAI,sBAAsB;AAC1B,UAAI,yBAAyB;AACzB,YAAI,YAAY,IAAI,UAAU;AAC9B,YAAI,oBAAoB,wBAAwB;AAChD,YAAI,4BAA4B,GAAG;AAG/B,kBAAQ,KAAK,OAAO,UAAU,QAAQ,mBAAmB,IAAI;AAAA,QACjE,OACK;AACD,oBAAU,KAAK,OAAO,QAAQ,iBAAiB;AAAA,QACnD;AACA,8BAAsB,2BAA2B,KAAK,OAAO,2BAA2B,GAAG,QAAQ,iBAAiB;AACpH,kBAAU,KAAK,OAAO,QAAQ,iBAAiB;AAAA,MACnD;AAEA,UAAI,kBAAkB,sBAAsB;AAE5C,6BAAuB,MAAM,CAAC,EAAE,QAAQ,SAAU,gBAAgB;AAC9D,YAAI,YAAY,IAAI,UAAU;AAG9B,gBAAQ,KAAK,OAAO,UAAU,QAAQ,eAAe,SAAS,IAAI;AAClE,mCAA2B,KAAK,OAAO,2BAA2B,GAAG,QAAQ,eAAe,SAAS,eAAe;AACpH,kBAAU,KAAK,OAAO,QAAQ,eAAe,OAAO;AAAA,MACxD,CAAC;AACD,iCAA2B;AAAA,IAC/B;AACA,WAAO,2BAA2B,MAAM,KAAK,SAAS,GAAG;AACrD,cAAQ;AAAA,IACZ;AAAA,EACJ;AACJ;AACA,SAAS,UAAU,KAAK,OAAO,QAAQ,SAAS;AAC5C,MAAI,WAAW,MAAM;AACrB,MAAI,YAAY,IAAI,UAAU;AAC9B,MAAI,SAAS,aAAa,eAAe,SAAS,aAAa,aAAa;AACxE,UAAM,KAAK,QAAQ,SAAU,KAAK;AAAE,aAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,IAAG,CAAC;AAAA,EAC5F;AACJ;AACA,SAAS,UAAU,KAAK,OAAO,UAAU,QAAQ,SAAS;AACtD,MAAI,YAAY,IAAI,UAAU;AAC9B,QAAM,KAAK,QAAQ,SAAU,KAAK,OAAO;AACrC,QAAI,YAAY,UAAU,MAAM,KAAK,SAAS;AAC9C,iBAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ,OAAO;AAAA,EACtE,CAAC;AACL;AACA,SAAS,2BAA2B,KAAK,OAAO,eAAe,QAAQ,SAAS,iBAAiB;AAC7F,MAAI,YAAY,IAAI,UAAU;AAC9B,oBAAkB,oBAAoB,QAAQ,oBAAoB,SAAS,kBAAkB,MAAM,KAAK;AACxG,MAAI,cAAc,KAAK,IAAI,gBAAgB,iBAAiB,MAAM,KAAK,MAAM;AAC7E,MAAI,sBAAsB;AAC1B,QAAM,KAAK,MAAM,eAAe,WAAW,EAAE,QAAQ,SAAU,KAAK,OAAO;AACvE,QAAI,YAAY,gBAAgB,UAAU,MAAM,KAAK,SAAS;AAC9D,QAAI,iBAAiB,sBAAsB,KAAK,OAAO,WAAW,MAAM;AACxE,QAAI,IAAI,gBAAgB,gBAAgB,OAAO,GAAG;AAC9C,eAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AACzC,4BAAsB,gBAAgB;AAAA,IAC1C;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AACA,SAAS,UAAU,KAAK,OAAO,QAAQ,SAAS;AAC5C,MAAI,WAAW,MAAM;AACrB,MAAI,YAAY,IAAI,UAAU;AAC9B,MAAI,SAAS,aAAa,cAAc,SAAS,aAAa,aAAa;AACvE,UAAM,KAAK,QAAQ,SAAU,KAAK;AAAE,aAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,IAAG,CAAC;AAAA,EAC5F;AACJ;AACA,SAAS,sBAAsB,MAAM,oBAAoB,KAAK;AAC1D,MAAI,aAAa,IAAI,cAAc,KAAK,OAAO,QAAQ;AACvD,MAAI,WAAW,KAAK,QAAQ,UAAU;AACtC,MAAI,iBAAiB,KAAK,OAAO,qBAAqB,YAAY,UAAU;AAC5E,SAAO,KAAK,IAAI,GAAG,cAAc;AACrC;AACA,SAAS,eAAe,KAAK,oBAAoB,OAAO,KAAK;AACzD,MAAI,QAAQ,CAAC;AACb,MAAI,qBAAqB;AACzB,MAAI,SAAS;AACb,MAAI,YAAY;AAChB,WAAS,KAAK,GAAGA,MAAK,MAAM,SAAS,KAAKA,IAAG,QAAQ,MAAM;AACvD,QAAI,SAASA,IAAG,EAAE;AAClB,QAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,QAAI,CAAC;AACD;AACJ,QAAI,CAAC,MAAM,QAAQ,KAAK,IAAI,GAAG;AAC3B,WAAK,OAAO,CAAC,KAAK,IAAI;AAAA,IAC1B;AACA,QAAI,gBAAgB,IAAI,KAAK,KAAK,KAAK,KAAK,QAAQ,KAAK,OAAO;AAChE,oBAAgB,OAAO,eAAe,IAAI;AAC1C,kBAAc,OAAO,CAAC;AACtB,QAAI,qBAAqB,sBAAsB,MAAM,oBAAoB,GAAG;AAC5E,QAAI,KAAK,KAAK,SAAS,oBAAoB;AACvC,oBAAc,OAAO,KAAK,KAAK,OAAO,oBAAoB,KAAK,KAAK,MAAM;AAAA,IAC9E;AACA,QAAI,cAAc,IAAI,YAAY;AAClC,QAAI,mBAAmB,IAAI,oBAAoB;AAC/C,SAAK,gBAAgB,KAAK,iBAAiB,aAAa,gBAAgB;AACxE,QAAI,KAAK,iBAAiB,oBAAoB;AAC1C,WAAK,gBAAgB;AACrB,oBAAc,OAAO,iBAAiB;AAAA,IAC1C;AACA,QAAI,KAAK,gBAAgB,IAAI,QAAQ;AACjC,UAAI,SAAS,KAAK;AAAA,IACtB;AACA,kBAAc,gBAAgB,cAAc,iBAAiB,aAAa,gBAAgB;AAC1F,QAAI,cAAc,gBAAgB,WAAW;AACzC,kBAAY,cAAc;AAAA,IAC9B;AACA,UAAM,OAAO,KAAK,IAAI;AAAA,EAC1B;AACA,MAAI,eAAe,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,SAAS,OAAO,IAAI;AAChE,eAAa,SAAS;AACtB,WAAS,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK,GAAG,QAAQ,MAAM;AACvD,QAAI,SAAS,GAAG,EAAE;AAClB,QAAI,gBAAgB,aAAa,MAAM,OAAO,KAAK;AACnD,QAAI,eAAe;AACf,oBAAc,SAAS,aAAa;AAAA,IACxC;AACA,QAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,QAAI,MAAM;AACN,WAAK,SAAS,IAAI;AAAA,IACtB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,yBAAyB,KAAK,KAAK,oBAAoB,OAAO;AACnE,MAAI,aAAa,IAAI,SAAS,EAAE;AAChC,MAAI,SAAS,MAAM,SAAS;AAC5B,MAAI,eAAe,OAAO,MAAM,OAAO;AACvC,MAAI,eAAe,aAAa;AAChC,MAAI,IAAI,YAAY,QAAQ;AAGxB,oBACI,MAAM,cAAc,MAAM,OAAO,IAAI,MAAM,cAAc,MAAM,OAAO;AAAA,EAC9E;AACA,MAAI,eAAe,IAAI,oBAAoB,MAAM,SAAS,GAAG;AAC7D,MAAI,aAAa,eAAe;AAChC,MAAI,eAAe,cAAc;AAC7B,YAAQ,MAAM,iCAAiC,OAAO,IAAI,OAAO,iEAAiE,CAAC;AACnI,WAAO;AAAA,EACX;AACA,MAAI,CAAC,YAAY;AACb,WAAO;AAAA,EACX;AACA,MAAI,oBAAoB,IAAI,WAAW,MAAM,OAAO;AACpD,MAAI,oBAAoB,IAAI,iBAAiB,MAAM,OAAO,IAAI;AAC9D,MAAI,mBAAmB;AACnB,QAAI,mBAAmB;AACnB,cAAQ,MAAM,sBAAsB,OAAO,IAAI,OAAO,yIAAyI,CAAC;AAAA,IACpM;AACA,WAAO;AAAA,EACX;AACA,MAAI,mBAAmB;AAEnB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,SAAS,iBAAiB,SAAS;AACzC,WAAO;AAAA,EACX;AAEA,SAAO;AACX;AACA,SAAS,aAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ,SAAS;AACzE,MAAI,iBAAiB,sBAAsB,KAAK,OAAO,WAAW,MAAM;AACxE,MAAI,IAAI,gBAAgB,gBAAgB,OAAO,GAAG;AAE9C,aAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,EAC7C,WACS,yBAAyB,KAAK,KAAK,gBAAgB,KAAK,GAAG;AAEhE,QAAI,eAAe,eAAe,KAAK,gBAAgB,OAAO,GAAG;AACjE,aAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AACzC,YAAQ,KAAK,OAAO,UAAU,QAAQ,OAAO;AAC7C,iBAAa,KAAK,OAAO,cAAc,WAAW,UAAU,QAAQ,OAAO;AAAA,EAC/E,OACK;AAED,YAAQ,KAAK,OAAO,UAAU,QAAQ,OAAO;AAC7C,iBAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ,OAAO;AAAA,EACtE;AACJ;AACA,SAAS,SAAS,KAAK,OAAO,KAAK,QAAQ,SAAS;AAChD,SAAO,IAAI,MAAM,SAAS,OAAO;AACjC,WAAS,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AAC/D,QAAI,SAAS,UAAU,EAAE;AACzB,QAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,QAAI,CAAC,MAAM;AACP,aAAO,KAAK,OAAO;AACnB;AAAA,IACJ;AACA,QAAI,YAAY,KAAK,MAAM;AAC3B,SAAK,IAAI,OAAO;AAChB,SAAK,IAAI,OAAO;AAChB,QAAI,SAAS,MAAM,cAAc,KAAK,MAAM,MAAM,cAAc,MAAM,KAAK,QAAQ,MAAM;AACzF,QAAI,WAAW,OAAO;AAClB,aAAO,KAAK,OAAO;AACnB;AAAA,IACJ;AACA,iBAAa,KAAK,MAAM,MAAM;AAC9B,QAAI,UAAU,KAAK,WAAW;AAC9B,kBAAc,KAAK,MAAM,QAAQ,GAAG,QAAQ,GAAG;AAAA,MAC3C,QAAQ,KAAK,OAAO;AAAA,MACpB,QAAQ,KAAK,OAAO;AAAA,MACpB,UAAU,KAAK,KAAK,KAAK,QAAQ,KAAK,QAAQ,MAAM,IAAI,KAAK,QAAQ,OAAO,CAAC;AAAA,IACjF,GAAG,IAAI,YAAY,CAAC;AACpB,UAAM,cAAc,KAAK,MAAM,MAAM,aAAa,MAAM,KAAK,QAAQ,MAAM;AAC3E,WAAO,KAAK,OAAO;AAAA,EACvB;AACA,SAAO,KAAK,IAAI;AACpB;AACA,SAAS,aAAa,KAAK,MAAM,QAAQ;AACrC,MAAIE,cAAa,KAAK;AAGtB,MAAI,YAAY,EAAE,aAAa,IAAI,YAAY,EAAE,aAAa,CAAC;AAC/D,MAAI,OAAOA,YAAW,cAAc,UAAU;AAE1C,QAAI,YAAY,aAAaA,YAAW,WAAWA,YAAW,SAAS;AACvE,QAAI,WAAW;AACX,UAAI,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK,OAAO,KAAK,QAAQ,SAAS;AAAA,IACjE;AAAA,EACJ,WACS,OAAOA,YAAW,cAAc,UAAU;AAE/C,QAAIA,YAAW,WAAW;AACtB,UAAI,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK,OAAO,KAAK,QAAQ,GAAG;AAAA,IAC3D;AAEA,oBAAgB,KAAK,MAAM,QAAQA,YAAW,SAAS;AAAA,EAC3D;AACJ;AAUA,SAAS,gBAAgB,KAAK,MAAM,QAAQ,WAAW;AACnD,MAAI,IAAI,IAAI,IAAI;AAChB,MAAI,UAAU,KAAK;AACf,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,OAAO,IAAI,KAAK;AACrB,SAAK,OAAO;AACZ,QAAI,UAAU,OAAO;AACjB,YAAM,MAAM,UAAU;AAAA,IAC1B;AACA,QAAI,UAAU,MAAM;AAChB,YAAM,MAAM,UAAU;AAAA,IAC1B;AACA,aAAS,UAAU,KAAK,IAAI,IAAI,IAAI,EAAE;AAAA,EAC1C;AACA,MAAI,UAAU,QAAQ;AAClB,SAAK,OAAO;AACZ,SAAK,OAAO,IAAI,KAAK;AACrB,SAAK,OAAO,IAAI,KAAK;AACrB,SAAK,OAAO,IAAI,KAAK;AACrB,QAAI,UAAU,OAAO;AACjB,YAAM,MAAM,UAAU;AAAA,IAC1B;AACA,QAAI,UAAU,MAAM;AAChB,YAAM,MAAM,UAAU;AAAA,IAC1B;AACA,aAAS,UAAU,QAAQ,IAAI,IAAI,IAAI,EAAE;AAAA,EAC7C;AACA,MAAI,UAAU,MAAM;AAChB,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,OAAO,IAAI,KAAK;AACrB,QAAI,UAAU,KAAK;AACf,YAAM,MAAM,UAAU;AAAA,IAC1B;AACA,QAAI,UAAU,QAAQ;AAClB,YAAM,MAAM,UAAU;AAAA,IAC1B;AACA,aAAS,UAAU,MAAM,IAAI,IAAI,IAAI,EAAE;AAAA,EAC3C;AACA,MAAI,UAAU,OAAO;AACjB,SAAK,OAAO,IAAI,KAAK;AACrB,SAAK,OAAO;AACZ,SAAK,OAAO,IAAI,KAAK;AACrB,SAAK,OAAO,IAAI,KAAK;AACrB,QAAI,UAAU,KAAK;AACf,YAAM,MAAM,UAAU;AAAA,IAC1B;AACA,QAAI,UAAU,QAAQ;AAClB,YAAM,MAAM,UAAU;AAAA,IAC1B;AACA,aAAS,UAAU,OAAO,IAAI,IAAI,IAAI,EAAE;AAAA,EAC5C;AACA,WAAS,SAAS,OAAOS,KAAIC,KAAIC,KAAIC,KAAI;AACrC,QAAI,YAAY,EAAE,aAAa,KAAK;AACpC,QAAI,YAAY,EAAE,KAAKH,KAAIC,KAAIC,KAAIC,KAAI,GAAG;AAAA,EAC9C;AACJ;AACA,SAAS,sBAAsB,KAAK,OAAO,WAAW,QAAQ;AAC1D,MAAI,sBAAsB,MAAM,SAAS,OAAO;AAChD,MAAI,WAAW,MAAM,SAAS;AAC9B,MAAI,aAAa,eAAgB,aAAa,cAAc,WAAY;AACpE,2BAAuB,MAAM,cAAc,MAAM,OAAO;AAAA,EAC5D;AACA,SAAO,IAAI,SAAS,EAAE,SAAS,OAAO,IAAI;AAC9C;AACA,SAAS,QAAQ,KAAK,OAAO,UAAU,QAAQ,SAAS,gBAAgB;AACpE,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,mBAAmB,QAAQ;AAAE,qBAAiB;AAAA,EAAO;AACzD,MAAI,YAAY,IAAI,UAAU;AAC9B,MAAI,MAAM,SAAS,aAAa,eAAe,CAAC,gBAAgB;AAC5D,UAAM,KAAK,QAAQ,SAAU,KAAK;AAAE,aAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,IAAG,CAAC;AAAA,EAC5F;AAGA,QAAM,iBAAiB,KAAK,MAAM;AAClC,MAAI,SAAS,MAAM,SAAS;AAC5B,iBAAe,KAAK,OAAO,UAAU,MAAM;AAC3C,WAAS,GAAG;AACZ,QAAM;AACN,SAAO,IAAI,OAAO;AAClB,SAAO,IAAI,OAAO;AAClB,WAAS,IAAI,OAAO;AAEpB,QAAM,sBAAsB,KAAK,MAAM;AACvC,MAAI,MAAM,SAAS,aAAa,aAAa;AACzC,UAAM,KAAK,QAAQ,SAAU,KAAK;AAAE,aAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,IAAG,CAAC;AACxF,QAAI,YAAY,IAAI,UAAU;AAAA,EAClC;AACJ;AACA,SAAS,SAAS,KAAK;AACnB,MAAI,UAAU,IAAI,WAAW;AAC7B,MAAI,QAAQ,UAAU,CAAC;AACvB,MAAI,aAAa,IAAI,WAAW;AAChC,MAAI,eAAe,SAAS;AACxB,QAAI,QAAQ;AACZ,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,SAAS,YAAY,OAAO;AAExB,QAAM,IAAI,YAAY,WAAY;AAC9B,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,QAAI,UAAU,KAAK,CAAC;AACpB,QAAI,QAAQ,WAAW,MAAM,OAAO;AACpC,QAAI,QAAQ,YAAY,MAAM,KAAK;AACnC,cAAU,MAAM,KAAK;AACrB,WAAO;AAAA,EACX;AAEA,QAAM,IAAI,gBAAgB;AAC1B,QAAM,IAAI,gBAAgB,SAAU,MAAM,GAAG,GAAG,QAAQ;AACpD,kBAAc,MAAM,GAAG,GAAG,QAAQ,IAAI;AAAA,EAC1C;AACA,QAAM,IAAI,uBAAuB,SAAU,UAAU;AACjD,eAAW,YAAY,UAAU,IAAI;AACrC,WAAO;AAAA,EACX;AACA,QAAM,uBAAuB,SAAU,UAAU,KAAK;AAClD,eAAW,YAAY,UAAU,GAAG;AAAA,EACxC;AACA,QAAM,IAAI,sBAAsB,SAAU,WAAW,uBAAuB;AACxE,QAAId;AACJ,QAAI,0BAA0B,QAAQ;AAAE,8BAAwB;AAAA,IAAO;AACvE,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,MAAM,2DAA2D;AACzE,aAAO;AAAA,IACX;AACA,QAAI,MAAM,IAAI,WAAW,IAAI;AAC7B,QAAI,KAAK,UAAU,KAAK,WAAW,QAAQ,uBAAuB,KAAK,GAAG,OAAO,GAAG,MAAM,OAAO,GAAG;AACpG,QAAI,YAAYA,MAAK,KAAK,CAAC,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,IAAI,SAAU,GAAG;AAAE,aAAO,EAAE;AAAA,IAAS,CAAC,MAAM,CAAC;AACnH,WAAO,EAAE,SAAkB,MAAM,MAAM,MAAM,KAAK;AAAA,EACtD;AACJ;AAEA,IAAI;AACJ,SAAS,UAAU,GAAG,SAAS;AAC3B,MAAI,QAAQ,WAAW,GAAG,OAAO;AACjC,MAAI,QAAQ,YAAY,GAAG,KAAK;AAChC,YAAU,GAAG,KAAK;AACtB;AAEA,SAAS,cAAc,GAAG,SAAS;AAC/B,MAAI,QAAQ,WAAW,GAAG,OAAO;AACjC,SAAO,YAAY,GAAG,KAAK;AAC/B;AACA,SAAS,YAAY,GAAG,OAAO;AAC3B,YAAU,GAAG,KAAK;AACtB;AACA,IAAI;AACA,MAAI,OAAO,WAAW,eAAe,QAAQ;AAErC,gBAAY;AACZ,YAAQ,UAAU,WAAW,KAAK,UAAU,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC/F,QAAI,OAAO;AACP,kBAAY,KAAK;AAAA,IACrB;AAAA,EACJ;AACJ,SACO,OAAO;AACV,UAAQ,MAAM,oCAAoC,KAAK;AAC3D;AATY;AACA;", "names": ["<PERSON><PERSON><PERSON><PERSON>", "_a", "d", "b", "HtmlRowInput", "text", "_a", "window", "cellStyles", "prop", "HookData", "CellHookData", "Table", "Row", "Cell", "Column", "column", "x1", "y1", "x2", "y2"]}