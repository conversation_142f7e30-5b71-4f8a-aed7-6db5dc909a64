# python_pdf_processor/run.py

import os
import sys # لإدارة مسار بايثون إذا لزم الأمر
import logging
from flask import Flask
from flask_cors import CORS

# --- BEGIN PYTHON PATH ADJUSTMENT ---
try:
    project_root = os.path.abspath(os.path.dirname(__file__))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
        # print(f"DEBUG: Added '{project_root}' to sys.path")
except Exception as e:
    # print(f"DEBUG: Error adjusting sys.path: {e}")
    pass
# --- END PYTHON PATH ADJUSTMENT ---


# --- BEGIN PRIMARY APPLICATION LOGGER CONFIGURATION ---
LOG_FORMAT = '%(asctime)s - %(levelname)s - [%(name)s:%(module)s:%(lineno)d] - %(message)s'
LOG_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

logging.basicConfig(level=logging.INFO, format=LOG_FORMAT, datefmt=LOG_DATE_FORMAT)
logging.getLogger('werkzeug').setLevel(logging.WARNING)
# --- END PRIMARY APPLICATION LOGGER CONFIGURATION ---


# --- BEGIN APP CREATION FUNCTION ---
def create_app():
    app_factory_logger = logging.getLogger(__name__ + ".create_app")
    
    app_factory_logger.info("=====================================================================")
    app_factory_logger.info("INITIALIZING PYTHON PDF PROCESSOR FLASK APPLICATION")
    app_factory_logger.info("=====================================================================")

    app = Flask(__name__)
    app_factory_logger.info("Flask app instance created.")

    # --- BEGIN CONFIGURATION LOADING ---
    try:
        from app.config import Config as AppConfigModule 
        app.config.from_object(AppConfigModule)
        app_factory_logger.info("Successfully loaded configuration from app.config.Config.")
    except ImportError as e:
        app_factory_logger.error(f"FAILED to import app.config.Config: {e}. Check PYTHONPATH and module location (app/config.py). Ensure __init__.py files exist.", exc_info=True)
        app_factory_logger.error("Application will rely on default Flask config and environment variables if Flask picks them up.")
        if 'UPLOADS_FOLDER' not in app.config:
             project_root_local = os.path.abspath(os.path.dirname(__file__))
             app.config['UPLOADS_FOLDER'] = os.path.join(project_root_local, 'uploads')
             app_factory_logger.warning(f"Using default UPLOADS_FOLDER due to config import failure: {app.config['UPLOADS_FOLDER']}")
    except Exception as e:
        app_factory_logger.error(f"An unexpected error occurred loading configuration: {e}", exc_info=True)
    # --- END CONFIGURATION LOADING ---

    # --- CORS SETUP ---
    CORS(app, resources={r"/api/*": {"origins": "*"}}) # Adjust origins for production
    app_factory_logger.info("CORS enabled for /api/* routes (currently allowing all origins).")
    # --- END CORS SETUP ---

    # --- BEGIN UPLOADS DIRECTORY CREATION ---
    app_factory_logger.info("---------------------------------------------------------------------")
    app_factory_logger.info("CONFIGURING UPLOADS DIRECTORY")
    app_factory_logger.info("---------------------------------------------------------------------")
    if app.config.get('UPLOADS_FOLDER'):
        try:
            os.makedirs(app.config['UPLOADS_FOLDER'], exist_ok=True)
            app_factory_logger.info(f"Uploads folder successfully configured/verified at: {app.config['UPLOADS_FOLDER']}")
        except OSError as e:
            app_factory_logger.error(f"CRITICAL ERROR: Could not create uploads directory {app.config['UPLOADS_FOLDER']}: {e}")
            app_factory_logger.error("Application might not function correctly without the uploads directory.")
    else:
        app_factory_logger.error("UPLOADS_FOLDER is not configured. File uploads will likely fail.")
    # --- END UPLOADS DIRECTORY CREATION ---

    # --- CONFIGURATION CHECKS ---
    if not app.config.get('GEMINI_API_KEY'):
        app_factory_logger.warning("GEMINI_API_KEY is not set. AI functionalities will fail.")
    else:
        app_factory_logger.info("GEMINI_API_KEY found in configuration.")
        app_factory_logger.info(f"GEMINI_SUMMARIZATION_MODEL is set to: {app.config.get('GEMINI_SUMMARIZATION_MODEL')}")


    if not app.config.get('NODE_MINDMAP_SERVICE_URL'): # For the mindmap feature
        app_factory_logger.warning("NODE_MINDMAP_SERVICE_URL is not set. Mindmap generation calls to Node.js will fail.")
    else:
        app_factory_logger.info(f"NODE_MINDMAP_SERVICE_URL is set to: {app.config.get('NODE_MINDMAP_SERVICE_URL')}")
    
    app_factory_logger.info(f"Redis Host (for caching): {app.config.get('REDIS_HOST')}:{app.config.get('REDIS_PORT')}")

    # --- BEGIN BLUEPRINT REGISTRATIONS ---
    app_factory_logger.info("---------------------------------------------------------------------")
    app_factory_logger.info("REGISTERING BLUEPRINTS")
    app_factory_logger.info("---------------------------------------------------------------------")
    
    registered_blueprints_count = 0
    failed_blueprints_count = 0

    def register_blueprint_with_logging(bp_name, import_function, url_prefix, friendly_name):
        nonlocal registered_blueprints_count, failed_blueprints_count
        app_factory_logger.info(f"Attempting to register blueprint: '{friendly_name}' (variable name: {bp_name})...")
        try:
            blueprint_module = import_function()
            app.register_blueprint(blueprint_module, url_prefix=url_prefix)
            app_factory_logger.info(f"SUCCESS: Blueprint '{friendly_name}' registered at prefix '{url_prefix}'.")
            registered_blueprints_count += 1
        except ImportError as e:
            app_factory_logger.error(f"FAILED to import module for blueprint '{friendly_name}' ({bp_name}): {e}", exc_info=True)
            app_factory_logger.error(f"  -> Check Python path, module location, and __init__.py files.")
            failed_blueprints_count += 1
        except AttributeError as e:
            app_factory_logger.error(f"FAILED: Attribute error. Could not find blueprint variable '{bp_name}' for '{friendly_name}': {e}", exc_info=True)
            app_factory_logger.error(f"  -> Ensure '{bp_name}' is correctly defined in its module.")
            failed_blueprints_count += 1
        except ValueError as e: 
            app_factory_logger.error(f"FAILED: Value error during registration of blueprint '{friendly_name}' ({bp_name}): {e}", exc_info=True)
            failed_blueprints_count += 1
        except Exception as e:
            app_factory_logger.error(f"FAILED: An unexpected error registering blueprint '{friendly_name}' ({bp_name}): {e}", exc_info=True)
            failed_blueprints_count += 1

    # 1. PDF Processing and Mindmap Orchestration Blueprint (EXISTING - called by Frontend)
    # This blueprint handles the original flow: Frontend -> Python (this route) -> Node.js (for mindmap from summary) -> Python -> Frontend
    register_blueprint_with_logging(
        bp_name="pdf_processing_bp", 
        import_function=lambda: __import__('app.routes.pdf.mindmap_genrator', fromlist=['pdf_processing_bp']).pdf_processing_bp,
        url_prefix='/api/pdf', # e.g., /api/pdf/process-pdf-for-mindmap
        friendly_name="PDF Processing & Mindmap Orchestration (Frontend Facing)"
    )

    # 2. Internal PDF Summarization Blueprint (NEW - called by Node.js)
    # This blueprint handles the new flow: Frontend -> Node.js -> Python (this route) -> Node.js -> Frontend
    register_blueprint_with_logging(
        bp_name="internal_pdf_summarization_bp", # As defined in internal_pdf_summarization_routes.py
        import_function=lambda: __import__('app.routes.pdf.internal_pdf_summarization_routes', fromlist=['internal_pdf_summarization_bp']).internal_pdf_summarization_bp,
        url_prefix='/api/internal/pdf-summarization', # e.g., /api/internal/pdf-summarization/summarize-uploaded-pdf
        friendly_name="Internal PDF Summarization Service (for Node.js)"
    )
    
    # 3. PDF Chat Blueprint (NEW - for text extraction for chat)
    register_blueprint_with_logging(
        bp_name="pdf_chat_bp",
        import_function=lambda: __import__('app.routes.pdf.chat_routes', fromlist=['pdf_chat_bp']).pdf_chat_bp,
        url_prefix='/api/chat', # e.g., /api/chat/pdf/extract-text
        friendly_name="PDF Chat - Text Extraction Service"
    )

    # 4. Investor Pitch Blueprint (NEW - for investor pitch generation)
    register_blueprint_with_logging(
        bp_name="investor_pitch_bp",
        import_function=lambda: __import__('app.routes.pdf.investor_pitch_routes', fromlist=['investor_pitch_bp']).investor_pitch_bp,
        url_prefix='/api/investor-pitch', # e.g., /api/investor-pitch/generate
        friendly_name="Investor Pitch Generation Service"
    )

    app_factory_logger.info("---------------------------------------------------------------------")
    app_factory_logger.info("BLUEPRINT REGISTRATION SUMMARY:")
    app_factory_logger.info(f"  Successfully registered: {registered_blueprints_count} blueprint(s).")
    if failed_blueprints_count > 0:
        app_factory_logger.warning(f"  Failed to register: {failed_blueprints_count} blueprint(s). Check logs above for details.")
    else:
        app_factory_logger.info("  All attempted blueprints registered successfully.")
    app_factory_logger.info("---------------------------------------------------------------------")
    # --- END BLUEPRINT REGISTRATIONS ---

    # --- SIMPLE ROOT ROUTE FOR HEALTH CHECK ---
    @app.route('/')
    def index():
        app.logger.debug("Health check endpoint '/' accessed.")
        return "Python PDF Processor (Flask Backend: Mindmap Orchestration & Internal Summarization) is running."

    app_factory_logger.info("Flask app initialization complete.")
    return app
# --- END APP CREATION FUNCTION ---


# --- MAIN APPLICATION EXECUTION ---
if __name__ == '__main__':
    main_logger = logging.getLogger(__name__ + ".__main__")
    
    app = create_app() 

    main_logger.info("---------------------------------------------------------------------")
    main_logger.info("STARTING FLASK DEVELOPMENT SERVER (via python run.py)")
    main_logger.info("---------------------------------------------------------------------")

    host = os.environ.get('FLASK_RUN_HOST', app.config.get('HOST', '0.0.0.0'))
    port = int(os.environ.get('FLASK_RUN_PORT', app.config.get('PORT', 5001))) # Default Python port
    debug_mode_env = os.environ.get('FLASK_DEBUG', str(app.config.get('DEBUG', True)))
    debug_mode = debug_mode_env.lower() in ('true', '1', 't')

    main_logger.info(f"Configuration for app.run: Host='{host}', Port={port}, DebugMode={debug_mode}")

    if debug_mode:
        main_logger.warning("Application is running in DEBUG MODE. Do not use in production with the Flask development server.")
        main_logger.warning("For production, use a WSGI server like Gunicorn or uWSGI.")
    else:
        main_logger.info("Application is running in PRODUCTION MODE (or debug explicitly disabled).")

    with app.app_context():
        main_logger.info("Registered URL Rules (Endpoints):")
        rules_output = []
        for rule in app.url_map.iter_rules():
            methods = ','.join(sorted([m for m in rule.methods if m not in ('OPTIONS', 'HEAD')]))
            rules_output.append(f"  - Endpoint: {rule.endpoint}, Methods: {methods}, Path: {rule}")
        
        if rules_output:
            for r_info in sorted(rules_output): 
                main_logger.info(r_info)
        else:
            main_logger.info("  No URL rules seem to be registered (this is unusual).")

    main_logger.info(f"Starting application server now. Access at http://{host if host != '0.0.0.0' else '127.0.0.1'}:{port}")
    try:
        app.run(host=host, port=port, debug=debug_mode)
    except Exception as e:
        main_logger.critical(f"FATAL ERROR: Failed to start Flask application server: {e}", exc_info=True)
    finally:
        main_logger.info("=====================================================================")
        main_logger.info("PYTHON PDF PROCESSOR FLASK APPLICATION SHUTDOWN (or attempt ended)")
        main_logger.info("=====================================================================")