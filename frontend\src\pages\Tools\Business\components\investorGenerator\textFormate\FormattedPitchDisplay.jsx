// File: ./components/InvestorPitch/FormattedPitchDisplay.jsx
// Purpose: Parses tagged pitch text and renders it using its OWN unique components.

import React, { useState, useMemo } from 'react';
import { FiClipboard, FiRefreshCw } from 'react-icons/fi';

// --- CORRECTED: Import the new, unique components from the InvestorPitch folder ---
import PitchHeader from './PitchHeader';
import PitchSubHeader from './PitchSubHeader';
import TheAskDisplay from './TheAskDisplay';

// Import RTL detection utility
import { isRTL } from '../../../../../../utils/textUtils';

const FormattedPitchDisplay = ({ pitchText, onReset }) => {
  const [copyButtonText, setCopyButtonText] = useState('Copy to Clipboard');

  // Detect text direction for RTL/LTR support
  const textDirection = useMemo(() => {
    if (!pitchText) return 'ltr';
    return isRTL(pitchText) ? 'rtl' : 'ltr';
  }, [pitchText]);

  const handleCopy = () => {
    // We copy the raw, tagged text, which is useful for users
    navigator.clipboard.writeText(pitchText)
      .then(() => {
        setCopyButtonText('Copied!');
        setTimeout(() => setCopyButtonText('Copy to Clipboard'), 2000);
      })
      .catch(err => {
        console.error('Failed to copy text: ', err);
        setCopyButtonText('Error copying');
      });
  };

  const renderContent = () => {
    if (!pitchText) return null;
    const lines = pitchText.split('\n').filter(line => line.trim() !== '');
    const renderedElements = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        
        // --- CORRECTED: Use the new, unique components ---
        if (line.startsWith('~H~')) {
            renderedElements.push(<PitchHeader key={i} line={line} />);
        } else if (line.startsWith('~S_SUB~')) {
            renderedElements.push(<PitchSubHeader key={i} line={line} />);
        } else if (line.startsWith('~ASK~')) {
            renderedElements.push(<TheAskDisplay key={i} line={line} />);
        } else if (line.startsWith('~P~')) {
            renderedElements.push(<p key={i} className="text-slate-300 leading-relaxed my-4">{line.substring(3).trim()}</p>);
        } else {
             // Fallback for any untagged or malformed lines
             renderedElements.push(<p key={i} className="text-slate-400">{line}</p>);
        }
    }
    return renderedElements;
  };

  return (
    <div className="w-full bg-slate-900/50 border border-slate-700 rounded-2xl p-6 md:p-8 animate-fade-in">

      {/* The structured content is rendered here */}
      <div
        className="prose prose-invert max-w-none mb-6"
        dir={textDirection}
        style={{
          textAlign: textDirection === 'rtl' ? 'right' : 'left',
          fontFamily: textDirection === 'rtl' ?
            "'Noto Sans Arabic', 'Arial Unicode MS', sans-serif" :
            "inherit"
        }}
      >
        {renderContent()}
      </div>

      {/* The action buttons */}
      <div className="flex flex-col sm:flex-row gap-4 border-t border-slate-700 pt-6 mt-6">
        <button
          onClick={handleCopy}
          className="flex-1 group flex items-center justify-center px-5 py-3 bg-yellow-500 hover:bg-yellow-600 text-slate-900 font-bold rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105"
        >
          <FiClipboard className="mr-2 w-5 h-5" />
          {copyButtonText}
        </button>
        <button
          onClick={onReset}
          className="flex-1 group flex items-center justify-center px-5 py-3 bg-slate-700 hover:bg-slate-600 text-white font-semibold rounded-lg transition-all duration-300"
        >
          <FiRefreshCw className="mr-2 w-5 h-5 transition-transform group-hover:rotate-180 duration-500" />
          Create Another Pitch
        </button>
      </div>
    </div>
  );
};

export default React.memo(FormattedPitchDisplay);