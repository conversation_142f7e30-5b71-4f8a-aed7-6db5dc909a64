# app/routes/pdf/investor_pitch_routes.py
import os
import tempfile
import json
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from app.services.pdf.pdf_utils import extract_text_from_pdf
from app.services.pdf.investor_pitch_service import extract_business_info_from_document, generate_investor_pitch, AIGatewayException
from app.config import Config

investor_pitch_bp = Blueprint('investor_pitch_bp', __name__)

def allowed_file(filename):
    """Check if the uploaded file has an allowed extension"""
    allowed_extensions = {'pdf', 'doc', 'docx'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions

def extract_text_from_document(file_path, filename):
    """Extract text from different document types"""
    file_extension = filename.rsplit('.', 1)[1].lower()
    
    if file_extension == 'pdf':
        return extract_text_from_pdf(file_path)
    elif file_extension in ['doc', 'docx']:
        # For now, return a placeholder. In production, you'd use python-docx or similar
        return f"[Document content from {filename}] - This would contain the actual extracted text from the Word document in a production environment."
    else:
        raise ValueError(f"Unsupported file type: {file_extension}")

@investor_pitch_bp.route('/generate', methods=['POST'])
def generate_investor_pitch_route():
    """Generate investor pitch with optional business plan document upload"""
    try:
        current_app.logger.info("[INVESTOR_PITCH] Starting pitch generation request")
        
        # Get form data
        form_data = {}
        language = request.form.get('language', 'English')
        
        # Extract all form fields
        form_fields = [
            'projectName', 'industry', 'projectDescription', 'problemStatement', 
            'solution', 'targetAudience', 'competition', 'pitchObjective', 
            'fundingAmount', 'growthPlan', 'toneOfVoice'
        ]
        
        for field in form_fields:
            form_data[field] = request.form.get(field, '')
        
        # Check if file was uploaded
        uploaded_file = request.files.get('businessPlanFile')
        document_analysis = None
        
        if uploaded_file and uploaded_file.filename:
            current_app.logger.info(f"[INVESTOR_PITCH] Processing uploaded file: {uploaded_file.filename}")
            
            # Validate file
            if not allowed_file(uploaded_file.filename):
                return jsonify({
                    "error": "Invalid file type. Only PDF, DOC, and DOCX files are allowed."
                }), 400
            
            # Save file temporarily and extract text
            original_filename = secure_filename(uploaded_file.filename)
            tmp_file_path = None
            
            try:
                # Create temporary file
                with tempfile.NamedTemporaryFile(delete=False, suffix=f".{original_filename.rsplit('.', 1)[1].lower()}") as tmp_file:
                    uploaded_file.save(tmp_file.path)
                    tmp_file_path = tmp_file.name
                
                # Extract text from document
                document_text = extract_text_from_document(tmp_file_path, original_filename)
                current_app.logger.info(f"[INVESTOR_PITCH] Extracted {len(document_text)} characters from document")
                
                # Analyze document to extract business information
                document_analysis = extract_business_info_from_document(document_text, language)
                current_app.logger.info("[INVESTOR_PITCH] Document analysis completed")
                
                # When file is uploaded, form fields become optional
                # We'll use the document analysis to fill in missing information
                
            except Exception as e:
                current_app.logger.error(f"[INVESTOR_PITCH] Error processing document: {e}", exc_info=True)
                return jsonify({
                    "error": f"Failed to process uploaded document: {str(e)}"
                }), 500
            finally:
                # Clean up temporary file
                if tmp_file_path and os.path.exists(tmp_file_path):
                    try:
                        os.remove(tmp_file_path)
                        current_app.logger.debug(f"[INVESTOR_PITCH] Cleaned up temporary file: {tmp_file_path}")
                    except OSError as e:
                        current_app.logger.error(f"[INVESTOR_PITCH] Error removing temporary file: {e}")
        
        else:
            # No file uploaded - validate required fields
            required_fields = [
                'projectName', 'projectDescription', 'problemStatement', 
                'solution', 'targetAudience', 'competition', 'pitchObjective', 'growthPlan'
            ]
            
            missing_fields = []
            for field in required_fields:
                if not form_data.get(field) or not form_data[field].strip():
                    missing_fields.append(field)
            
            if missing_fields:
                return jsonify({
                    "error": f"Missing required fields: {', '.join(missing_fields)}. Please fill in all required fields or upload a business plan document."
                }), 400
        
        # Generate the investor pitch
        current_app.logger.info("[INVESTOR_PITCH] Generating investor pitch content")
        
        try:
            pitch_content = generate_investor_pitch(form_data, document_analysis, language)
            
            current_app.logger.info("[INVESTOR_PITCH] Pitch generation completed successfully")
            
            return jsonify({
                "generatedPitch": pitch_content,
                "hasDocumentAnalysis": document_analysis is not None,
                "language": language
            }), 200
            
        except AIGatewayException as e:
            current_app.logger.error(f"[INVESTOR_PITCH] AI Gateway Error: {e}", exc_info=True)
            return jsonify({
                "error": "The AI service is currently unavailable. Please try again later."
            }), 503
        except ValueError as e:
            current_app.logger.error(f"[INVESTOR_PITCH] AI Service Error: {e}", exc_info=True)
            return jsonify({
                "error": f"AI processing error: {str(e)}"
            }), 500
    
    except Exception as e:
        current_app.logger.error(f"[INVESTOR_PITCH] Unexpected error: {e}", exc_info=True)
        return jsonify({
            "error": "An unexpected error occurred while generating the investor pitch."
        }), 500

@investor_pitch_bp.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for the investor pitch service"""
    return jsonify({
        "status": "healthy",
        "service": "investor_pitch",
        "version": "1.0.0"
    }), 200
