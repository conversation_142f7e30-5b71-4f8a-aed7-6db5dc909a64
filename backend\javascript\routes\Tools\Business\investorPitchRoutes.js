// src/routes/Tools/Business/investorPitchRoutes.js

import express from 'express';
import { generateInvestorPitch } from '../../../controllers/tools/BusinessPlan/investorPitchController.js';
import { protect } from '../../../middleware/authMiddleware.js';
import businessPlanUpload from '../../../config/businessPlanUploadConfig.js';

const router = express.Router();

// @desc    Generate a new investor pitch from user input
// @route   POST /api/investor-pitch/generate
// @access  Private (requires authentication)
router.post('/generate', protect, businessPlanUpload.single('businessPlanFile'), generateInvestorPitch);

export default router;